package com.hl.test.es.document;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.dromara.easyes.annotation.IndexField;
import org.dromara.easyes.annotation.IndexId;
import org.dromara.easyes.annotation.IndexName;

import java.time.LocalDateTime;
import java.util.List;

@IndexName
public class WjJqAnalysisDocument {
    /**
     * 接警编号
     */
    @IndexId(value = "jjbh")
    @ApiModelProperty(value="接警编号")
    private String jjbh;

    /**
     * 处警标识
     */
    @IndexField(value = "cjbs")
    @ApiModelProperty(value="处警标识")
    private String cjbs;

    /**
     * 接警报警人
     */
    @IndexField(value = "bjr")
    @ApiModelProperty(value="接警报警人")
    private String bjr;

    /**
     * 报警类型
     */
    @IndexField(value = "bjlx")
    @ApiModelProperty(value="报警类型")
    private String bjlx;

    /**
     * 接警单位名称
     */
    @IndexField(value = "jjdwmc")
    @ApiModelProperty(value="接警单位名称")
    private String jjdwmc;

    /**
     * 接警报警时间
     */
    @IndexField(value = "bjdhsj_time")
    @ApiModelProperty(value="接警报警时间")
    private LocalDateTime bjdhsjTime;

    /**
     * 接警单位
     */
    @IndexField(value = "jjdw")
    @ApiModelProperty(value="接警单位")
    private String jjdw;

    /**
     * 接警报警人联系电话
     */
    @IndexField(value = "lxdh")
    @ApiModelProperty(value="接警报警人联系电话")
    private String lxdh;

    /**
     * 报警内容
     */
    @IndexField(value = "bjnr")
    @ApiModelProperty(value="报警内容")
    private String bjnr;

    /**
     * 接警登记单位名称
     */
    @IndexField(value = "djdwmc")
    @ApiModelProperty(value="接警登记单位名称")
    private String djdwmc;

    /**
     * 发生地点
     */
    @IndexField(value = "sfdd")
    @ApiModelProperty(value="发生地点")
    private String sfdd;

    /**
     * 报警方式
     */
    @IndexField(value = "bjxs")
    @ApiModelProperty(value="报警方式")
    private String bjxs;

    /**
     * 接警登记时间
     */
    @IndexField(value = "djsj_time")
    @ApiModelProperty(value="接警登记时间")
    private LocalDateTime djsjTime;

    /**
     * 接警登记编号
     */
    @IndexField(value = "jjdbh")
    @ApiModelProperty(value="接警登记编号")
    private String jjdbh;
    
    /**
     * 修改时间
     */
    @IndexField(value = "xgsj_time")
    @ApiModelProperty(value="修改时间")
    private LocalDateTime xgsjTime;
    
    /**
     * 警情坐标-X
     */
    @IndexField(value = "gis_x")
    @ApiModelProperty(value="警情坐标-X")
    private String gisX;

    /**
     * 警情坐标-y
     */
    @IndexField(value = "gis_y")
    @ApiModelProperty(value="警情坐标-y")
    private String gisY;




    /**
     * 处警处警时间
     */
    @IndexField(value = "cjsj_time")
    @ApiModelProperty(value="处警处警时间")
    private LocalDateTime cjsjTime;

    /**
     * 处警类别
     */
    @IndexField(value = "cjlb")
    @ApiModelProperty(value="处警类别")
    private String cjlb;

    /**
     * 处警单位
     */
    @IndexField(value = "cjdw")
    @ApiModelProperty(value="处警单位")
    private String cjdw;

    /**
     * 事发场所
     */
    @IndexField(value = "sfcs")
    @ApiModelProperty(value="事发场所")
    private String sfcs;

    /**
     * 损失详细情况
     */
    @IndexField(value = "ssxxqk")
    @ApiModelProperty(value="损失详细情况")
    private String ssxxqk;

    /**
     * 补充处理结果
     */
    @IndexField(value = "bccljg")
    @ApiModelProperty(value="补充处理结果")
    private String bccljg;

    /**
     * 事发时间下限
     */
    @IndexField(value = "sfsjxx_time")
    @ApiModelProperty(value="事发时间下限")
    private LocalDateTime sfsjxxTime;

    /**
     * 事发星期
     */
    @IndexField(value = "sfxq")
    @ApiModelProperty(value="事发星期")
    private String sfxq;

    /**
     * 处理结果内容
     */
    @IndexField(value = "cljgnr")
    @ApiModelProperty(value="处理结果内容")
    private String cljgnr;

    /**
     * 事发时间上限
     */
    @IndexField(value = "sfsjsx_time")
    @ApiModelProperty(value="事发时间上限")
    private LocalDateTime sfsjsxTime;

    /**
     * 登记人
     */
    @IndexField(value = "djr")
    @ApiModelProperty(value="登记人")
    private String djr;

    /**
     * 警情属性
     */
    @IndexField(value = "jqsx")
    @ApiModelProperty(value="警情属性")
    private String jqsx;

    /**
     * 天气情况
     */
    @IndexField(value = "tqqk")
    @ApiModelProperty(value="天气情况")
    private String tqqk;

    /**
     * 处警编号
     */
    @IndexField(value = "cjbh")
    @ApiModelProperty(value="处警编号")
    private String cjbh;

    /**
     * 处警详址
     */
    @IndexField(value = "cjxz")
    @ApiModelProperty(value="处警详址")
    private String cjxz;

    /**
     * 处警处警单位名称
     */
    @IndexField(value = "cjdwmc")
    @ApiModelProperty(value="处警处警单位名称")
    private String cjdwmc;

    /**
     * 处警结果
     */
    @IndexField(value = "cjjg")
    @ApiModelProperty(value="处警结果")
    private String cjjg;


    /**
     * 所属辖区
     */
    @IndexField(value = "ssxq")
    @ApiModelProperty(value="所属辖区")
    private String ssxq;

    /**
     * 处警人
     */
    @IndexField(value = "cjr")
    @ApiModelProperty(value="处警人")
    private String cjr;

    /**
     * 警情标签
     */
    @IndexField(value = "cjjqbq")
    @ApiModelProperty(value="警情标签")
    private String cjjqbq;

    /**
     * 地址补充
     */
    @IndexField(value = "bzdzmc")
    @ApiModelProperty(value="地址补充")
    private String bzdzmc;

    /**
     * 区域类别
     */
    @IndexField(value = "qylb")
    @ApiModelProperty(value="区域类别")
    private String qylb;

    /**
     * 处警信息地点
     */
    @IndexField(value = "cjxxdd")
    @ApiModelProperty(value="处警信息地点")
    private String cjxxdd;

    /**
     * 警情坐标-X
     */
    @IndexField(value = "xzb")
    @ApiModelProperty(value="警情坐标-X")
    private String xzb;

    /**
     * 警情坐标-Y'
     */
    @IndexField(value = "yzb")
    @ApiModelProperty(value="警情坐标-Y'")
    private String yzb;


    /**
     * 分局标签
     */
    @IndexField(value = "fjbq")
    @ApiModelProperty(value="分局标签")
    private String fjbq;



    @IndexField(value = "bzzt")
    @ApiModelProperty(value="标注状态")
    private Integer bzzt;


    @IndexField(value = "dz_type")
    @ApiModelProperty(value="")
    private String dzType;

    @IndexField(value = "dsdz")
    @ApiModelProperty(value="")
    private String dsdz;

    @IndexField(value = "jgbh")
    @ApiModelProperty(value="")
    private String jgbh;

    @IndexField(value = "dzid")
    @ApiModelProperty(value="")
    private String dzid;

    @IndexField(value = "dwmc")
    @ApiModelProperty(value="")
    private String dwmc;

    @IndexField(value = "dwdz")
    @ApiModelProperty(value="")
    private String dwdz;

    @IndexField(value = "xq")
    @ApiModelProperty(value="")
    private String xq;

    @IndexField(value = "zrq")
    @ApiModelProperty(value="")
    private String zrq;


    /**
     * 标注单位
     */
    @IndexField(value = "unit")
    @ApiModelProperty(value="标注单位")
    private String unit;

    /**
     * 审批结果:-1未标注 0已标注待审批 1同意 2退回
     */
    @IndexField(value = "spjg")
    @ApiModelProperty(value="审批结果:-1未标注 0已标注待审批 1同意 2退回")
    private Integer spjg;

    /**
     * 审批人
     */
    @IndexField(value = "spr")
    @ApiModelProperty(value="审批人")
    private String spr;

    /**
     * 审批时间
     */
    @IndexField(value = "sp_time")
    @ApiModelProperty(value="审批时间")
    private String spTime;

    /**
     * 审批内容
     */
    @IndexField(value = "spnr")
    @ApiModelProperty(value="审批内容")
    private String spnr;

    /**
     * 标注人
     */
    @IndexField(value = "bzr")
    @ApiModelProperty(value="标注人")
    private String bzr;

    /**
     * 标注时间
     */
    @IndexField(value = "bz_time")
    @ApiModelProperty(value="标注时间")
    private String bzTime;

    /**
     * 0未标注1已标注
     */
    @IndexField(value = "mark")
    @ApiModelProperty(value="0未标注1已标注")
    private Integer mark;

    @IndexField(value = "bzrxm")
    @ApiModelProperty(value="")
    private String bzrxm;

    @IndexField(value = "sprxm")
    @ApiModelProperty(value="")
    private String sprxm;

    /**
     * 事发原因 
     */
    @IndexField(value = "fsyy")
    @ApiModelProperty(value="事发原因 ")
    private String fsyy;



    /**
     * 警情标注标签
     */
    @IndexField(value = "jqbz")
    @ApiModelProperty(value="警情标注标签")
    private String jqbz;


    @IndexField(value = "bz_list")
    private List<LabelInfo> bzList;



    /**
     * 地址标签
     */
    @IndexField(value = "addressM")
    @ApiModelProperty(value="地址标签")
    private String addressm;

    @IndexField(value = "address_list")
    private List<LabelInfo> addressList;

    /**
     * 人员标签
     */
    @IndexField(value = "personM")
    @ApiModelProperty(value="人员标签")
    private String personm;

    /**
     * 结果标签
     */
    @IndexField(value = "resultM")
    @ApiModelProperty(value="结果标签")
    private String resultm;

    /**
     * 时间标签
     */
    @IndexField(value = "timeM")
    @ApiModelProperty(value="时间标签")
    private String timem;

    /**
     * 手段标签
     */
    @IndexField(value = "toolM")
    @ApiModelProperty(value="手段标签")
    private String toolm;

    /**
     * 原因标签
     */
    @IndexField(value = "reasonM")
    @ApiModelProperty(value="原因标签")
    private String reasonm;


    @Data
    public static class LabelInfo{

        private String label;

        private String labelName;

        private String memo;

        private Integer type;

        private String unit;

    }

    @Data
    public static  class InvolvedPerson{

        private String gmsfhm;

        private String xm;

        private String sjlb;

        private List<LabelInfo> personLabels;

    }

}
