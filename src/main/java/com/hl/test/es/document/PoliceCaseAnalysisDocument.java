package com.hl.test.es.document;


import lombok.Data;
import org.dromara.easyes.annotation.IndexField;
import org.dromara.easyes.annotation.IndexId;
import org.dromara.easyes.annotation.IndexName;
import org.dromara.easyes.annotation.rely.FieldType;
import org.dromara.easyes.annotation.rely.IdType;

import java.util.Date;
import java.util.List;

/**
 * 警情分析ES文档对象
 * 用于优化警情统计、涉警人员统计、明细查询等接口
 */
@Data
@IndexName("police_case_analysis")
public class PoliceCaseAnalysisDocument {
    
    @IndexId(type = IdType.CUSTOMIZE)
    private String id; // 使用jjbh作为主键
    
    // === 基础警情信息 ===
    @IndexField(fieldType = FieldType.KEYWORD)
    private String jjbh; // 接警编号
    
    @IndexField(fieldType = FieldType.DATE, dateFormat = "yyyy-MM-dd HH:mm:ss||yyyy-MM-dd||epoch_millis")
    private Date bjdhsjTime; // 报警时间
    
    @IndexField(fieldType = FieldType.DATE, dateFormat = "yyyy-MM-dd HH:mm:ss||yyyy-MM-dd||epoch_millis")
    private Date bzTime; // 标注时间
    
    @IndexField(fieldType = FieldType.KEYWORD)
    private String jjdw; // 接警单位
    
    @IndexField(fieldType = FieldType.TEXT, analyzer = "ik_max_word")
    private String jjdwmc; // 接警单位名称
    
    @IndexField(fieldType = FieldType.KEYWORD)
    private String bjlx; // 报警类型
    
    @IndexField(fieldType = FieldType.KEYWORD)
    private String cjlb; // 处警类别
    
    @IndexField(fieldType = FieldType.KEYWORD)
    private String cjjg; // 处警结果
    
    @IndexField(fieldType = FieldType.TEXT, analyzer = "ik_max_word")
    private String bjr; // 报警人
    
    @IndexField(fieldType = FieldType.KEYWORD)
    private String lxdh; // 联系电话
    
    @IndexField(fieldType = FieldType.TEXT, analyzer = "ik_max_word")
    private String cjdwmc; // 处警单位名称
    
    @IndexField(fieldType = FieldType.TEXT, analyzer = "ik_max_word")
    private String cljgnr; // 处理结果内容
    
    // === 标签信息 ===
    @IndexField(fieldType = FieldType.NESTED)
    private List<LabelInfo> labels; // 所有标签信息
    
    @IndexField(fieldType = FieldType.TEXT, analyzer = "ik_max_word")
    private String addressLabels; // 地址标签（合并后的文本）
    
    @IndexField(fieldType = FieldType.TEXT, analyzer = "ik_max_word")
    private String personLabels; // 人员标签（合并后的文本）
    
    @IndexField(fieldType = FieldType.TEXT, analyzer = "ik_max_word")
    private String resultLabels; // 结果标签（合并后的文本）
    
    @IndexField(fieldType = FieldType.TEXT, analyzer = "ik_max_word")
    private String timeLabels; // 时间标签（合并后的文本）
    
    @IndexField(fieldType = FieldType.TEXT, analyzer = "ik_max_word")
    private String toolLabels; // 手段标签（合并后的文本）
    
    @IndexField(fieldType = FieldType.TEXT, analyzer = "ik_max_word")
    private String reasonLabels; // 原因标签（合并后的文本）
    
    // === 涉警人员信息 ===
    @IndexField(fieldType = FieldType.NESTED)
    private List<InvolvedPerson> involvedPersons; // 涉警人员列表
    
    // === 统计维度字段 ===
    @IndexField(fieldType = FieldType.KEYWORD)
    private String unitLevel; // 单位层级（省厅/市局/分局/派出所）
    
    @IndexField(fieldType = FieldType.KEYWORD)
    private String timeRange; // 时间范围标识（用于快速时间范围查询）
    
    @IndexField(fieldType = FieldType.INTEGER)
    private Integer involvedPersonCount; // 涉警人员数量
    
    @IndexField(fieldType = FieldType.BOOLEAN)
    private Boolean hasMultipleInvolved; // 是否有多次涉警人员
    
    // === 地理位置信息 ===
    @IndexField(fieldType = FieldType.GEO_POINT)
    private String location; // 地理位置坐标 "lat,lon"格式
    
    @IndexField(fieldType = FieldType.TEXT, analyzer = "ik_max_word")
    private String address; // 详细地址
    
    // === 审核状态 ===
    @IndexField(fieldType = FieldType.INTEGER)
    private Integer bzzt; // 标注状态
    
    @IndexField(fieldType = FieldType.INTEGER)
    private Integer spjg; // 审核结果
    
    // === 原始数据字段（用于兼容现有查询） ===
    @IndexField(fieldType = FieldType.TEXT, analyzer = "ik_max_word")
    private String jqbz; // 警情标注标签
    
    @IndexField(fieldType = FieldType.TEXT, analyzer = "ik_max_word")
    private String fjbq; // 分局标签
    
    @IndexField(fieldType = FieldType.TEXT, analyzer = "ik_max_word")
    private String cjjqbq; // 处警警情标签
    
    // === 内嵌对象定义 ===
    @Data
    public static class LabelInfo {
        @IndexField(fieldType = FieldType.KEYWORD)
        private String label; // 标签ID
        
        @IndexField(fieldType = FieldType.TEXT, analyzer = "ik_max_word")
        private String dictName; // 标签名称
        
        @IndexField(fieldType = FieldType.TEXT, analyzer = "ik_max_word")
        private String memo; // 标签备注
        
        @IndexField(fieldType = FieldType.INTEGER)
        private Integer type; // 标签类型
        
        @IndexField(fieldType = FieldType.KEYWORD)
        private String unit; // 所属单位
    }
    
    @Data
    public static class InvolvedPerson {
        @IndexField(fieldType = FieldType.KEYWORD)
        private String gmsfhm; // 身份证号
        
        @IndexField(fieldType = FieldType.TEXT, analyzer = "ik_max_word")
        private String xm; // 姓名
        
        @IndexField(fieldType = FieldType.KEYWORD)
        private String sjlb; // 涉警类别
        
        @IndexField(fieldType = FieldType.TEXT, analyzer = "ik_max_word")
        private String personLabels; // 人员相关标签
        
        @IndexField(fieldType = FieldType.INTEGER)
        private Integer involvedCount; // 该人员涉警总次数（需要额外计算）
    }
}
