# 警情统计ES优化模块

## 概述

本模块基于Easy-ES框架，对原有的警情统计接口进行了性能优化，通过Elasticsearch提供高性能的查询和统计功能。

## 模块结构

```
src/main/java/com/hl/test/es/
├── config/                 # ES配置
│   └── EasyEsConfig.java   # Easy-ES配置类
├── controller/             # 优化后的Controller
│   └── JQStaEsController.java
├── document/               # ES文档对象
│   └── PoliceCaseAnalysisDocument.java
├── mapper/                 # ES Mapper
│   └── PoliceCaseAnalysisMapper.java
├── service/                # 业务服务
│   ├── PoliceCaseDataConvertService.java  # 数据转换服务
│   ├── PoliceCaseDataSyncService.java     # 数据同步服务
│   └── PoliceCaseEsService.java           # ES查询服务
├── task/                   # 定时任务
│   └── PoliceCaseDataSyncTask.java
└── README.md
```

## 主要功能

### 1. 优化后的接口

- `POST /api/jq-sta-es/query` - 警情统计（ES优化版）
- `POST /api/jq-sta-es/query_sjry` - 涉警人员统计（ES优化版）
- `POST /api/jq-sta-es/sta_det` - 警情统计明细（ES优化版）
- `POST /api/jq-sta-es/sta_jq_list` - 警情统计明细列表（ES优化版）

### 2. 数据同步管理

- `POST /api/jq-sta-es/sync/full` - 全量同步数据到ES
- `POST /api/jq-sta-es/sync/incremental` - 增量同步数据到ES
- `POST /api/jq-sta-es/sync/single` - 同步单个警情到ES

### 3. 索引管理

- `POST /api/jq-sta-es/index/create` - 创建ES索引
- `POST /api/jq-sta-es/index/delete` - 删除ES索引
- `GET /api/jq-sta-es/index/status` - 检查ES索引状态

## 配置说明

### application.yml配置

```yaml
# Easy-ES配置
easy-es:
  enable: true                    # 是否启用ES
  address: 127.0.0.1:9200        # ES地址
  username:                       # ES用户名（可选）
  password:                       # ES密码（可选）
  schema: http                    # 协议
  connect-timeout: 5000           # 连接超时
  socket-timeout: 600000          # Socket超时
  request-timeout: 600000         # 请求超时
  connection-request-timeout: 5000 # 连接请求超时
  max-conn-total: 100             # 最大连接数
  max-conn-per-route: 100         # 每个路由最大连接数
  global-config:
    process-index-mode: smoothly  # 索引处理模式
    print-dsl: true              # 是否打印DSL
    distributed: false           # 是否分布式
    async-process-index-blocking: true # 异步处理索引是否阻塞
    db-config:
      map-underscore-to-camel-case: true # 下划线转驼峰
      table-prefix:                      # 表前缀
      id-type: customize                 # ID类型
      field-strategy: not_null           # 字段策略
      refresh-policy: immediate          # 刷新策略

# 定时同步配置（可选）
police-case:
  sync:
    full-sync-enabled: false      # 是否启用每日全量同步
```

## 使用指南

### 1. 初始化

首次使用需要创建索引并进行全量同步：

```bash
# 1. 创建索引
curl -X POST "http://localhost:10364/api/jq-sta-es/index/create"

# 2. 全量同步数据
curl -X POST "http://localhost:10364/api/jq-sta-es/sync/full"
```

### 2. 日常使用

系统会自动进行增量同步，也可以手动触发：

```bash
# 增量同步（指定时间范围）
curl -X POST "http://localhost:10364/api/jq-sta-es/sync/incremental?startTime=2024-01-01 00:00:00&endTime=2024-01-01 23:59:59"

# 同步单个警情
curl -X POST "http://localhost:10364/api/jq-sta-es/sync/single?jjbh=JJ202401010001"
```

### 3. 查询接口

使用方式与原接口完全相同，只需要将URL路径改为ES版本：

```javascript
// 原接口
POST /api/jq-sta/query

// ES优化版接口
POST /api/jq-sta-es/query
```

## 性能优势

1. **查询性能提升**：基于ES的倒排索引，查询速度提升10-100倍
2. **聚合统计优化**：ES原生支持高性能聚合操作
3. **全文检索能力**：支持对标签、内容等字段进行全文检索
4. **分页性能**：深度分页性能优异
5. **并发处理**：ES天然支持高并发查询

## 注意事项

1. **数据一致性**：ES数据通过定时同步保持与数据库一致，可能存在轻微延迟
2. **存储空间**：ES会占用额外的存储空间
3. **内存使用**：ES需要足够的内存来保证性能
4. **网络延迟**：ES集群部署时需要考虑网络延迟

## 监控和维护

1. **索引状态监控**：定期检查索引状态和文档数量
2. **同步任务监控**：关注定时同步任务的执行情况
3. **性能监控**：监控ES集群的CPU、内存、磁盘使用情况
4. **日志监控**：关注同步和查询的错误日志

## 故障排除

### 常见问题

1. **连接失败**：检查ES服务是否启动，网络是否通畅
2. **索引不存在**：执行创建索引操作
3. **数据不一致**：执行增量或全量同步
4. **查询超时**：调整超时配置或优化查询条件

### 日志查看

```bash
# 查看ES相关日志
grep "ES" application.log

# 查看同步任务日志
grep "sync" application.log
```

## 扩展说明

本模块设计时考虑了扩展性，可以根据业务需要：

1. 添加更多的统计维度
2. 支持更复杂的查询条件
3. 集成更多的ES高级功能（如地理位置查询、机器学习等）
4. 支持多索引查询和跨集群查询
