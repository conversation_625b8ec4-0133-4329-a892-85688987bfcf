package com.hl.test.es.example;

import com.alibaba.fastjson2.JSONObject;
import com.hl.test.domain.req.ReqJQStaQuery;
import com.hl.test.domain.resp.RespJQSta;
import com.hl.test.domain.resp.RespSjryBq;
import com.hl.test.domain.resp.RespSjryJqList;
import com.hl.test.es.service.PoliceCaseDataSyncService;
import com.hl.test.es.service.PoliceCaseEsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * 警情ES功能使用示例
 * 仅在开发环境下运行
 */
@Slf4j
@Component
@ConditionalOnProperty(name = "police-case.example.enabled", havingValue = "true")
public class PoliceCaseEsExample implements CommandLineRunner {
    
    @Autowired
    private PoliceCaseEsService policeCaseEsService;
    
    @Autowired
    private PoliceCaseDataSyncService dataSyncService;
    
    @Override
    public void run(String... args) throws Exception {
        log.info("开始执行警情ES功能示例");
        
        try {
            // 示例1：检查索引状态
            checkIndexStatus();
            
            // 示例2：创建索引（如果不存在）
            createIndexIfNotExists();
            
            // 示例3：执行警情统计查询
            executeJqStaQuery();
            
            // 示例4：执行涉警人员统计查询
            executeSjryStaQuery();
            
            // 示例5：执行警情明细查询
            executeStaDetQuery();
            
            // 示例6：执行警情列表查询
            executeJqListQuery();
            
        } catch (Exception e) {
            log.error("执行警情ES功能示例失败", e);
        }
        
        log.info("警情ES功能示例执行完成");
    }
    
    /**
     * 检查索引状态
     */
    private void checkIndexStatus() {
        log.info("=== 检查索引状态 ===");
        boolean exists = dataSyncService.indexExists();
        log.info("索引是否存在: {}", exists);
    }
    
    /**
     * 创建索引（如果不存在）
     */
    private void createIndexIfNotExists() {
        log.info("=== 创建索引 ===");
        if (!dataSyncService.indexExists()) {
            boolean success = dataSyncService.createIndex();
            log.info("索引创建结果: {}", success);
        } else {
            log.info("索引已存在，跳过创建");
        }
    }
    
    /**
     * 执行警情统计查询示例
     */
    private void executeJqStaQuery() {
        log.info("=== 警情统计查询示例 ===");
        
        try {
            ReqJQStaQuery params = new ReqJQStaQuery();
            params.setBjdhsj_time_start("2024-01-01 00:00:00");
            params.setBjdhsj_time_end("2024-12-31 23:59:59");
            params.setCjlb(Arrays.asList("110", "120")); // 示例处警类别
            
            List<RespJQSta> result = policeCaseEsService.queryJqSta(params, "test_unit");
            
            log.info("警情统计查询结果数量: {}", result.size());
            for (RespJQSta sta : result) {
                log.info("统计类型: {}, 名称: {}, 明细数量: {}", 
                        sta.getType(), sta.getName(), sta.getDets().size());
            }
            
        } catch (Exception e) {
            log.error("警情统计查询示例失败", e);
        }
    }
    
    /**
     * 执行涉警人员统计查询示例
     */
    private void executeSjryStaQuery() {
        log.info("=== 涉警人员统计查询示例 ===");
        
        try {
            ReqJQStaQuery params = new ReqJQStaQuery();
            params.setBjdhsj_time_start("2024-01-01 00:00:00");
            params.setBjdhsj_time_end("2024-12-31 23:59:59");
            
            List<RespJQSta> result = policeCaseEsService.queryJqStaSjry(params, "test_unit");
            
            log.info("涉警人员统计查询结果数量: {}", result.size());
            for (RespJQSta sta : result) {
                log.info("统计类型: {}, 名称: {}, 明细数量: {}", 
                        sta.getType(), sta.getName(), sta.getDets().size());
            }
            
        } catch (Exception e) {
            log.error("涉警人员统计查询示例失败", e);
        }
    }
    
    /**
     * 执行警情明细查询示例
     */
    private void executeStaDetQuery() {
        log.info("=== 警情明细查询示例 ===");
        
        try {
            ReqJQStaQuery params = new ReqJQStaQuery();
            params.setBjdhsj_time_start("2024-01-01 00:00:00");
            params.setBjdhsj_time_end("2024-12-31 23:59:59");
            params.setLabel("test_label"); // 示例标签
            
            RespSjryBq result = policeCaseEsService.queryStaDet(params, "test_unit", "test_label");
            
            log.info("警情明细查询完成");
            log.info("人员标签数量: {}", result.get_personM().size());
            log.info("地址标签数量: {}", result.get_addressM().size());
            
        } catch (Exception e) {
            log.error("警情明细查询示例失败", e);
        }
    }
    
    /**
     * 执行警情列表查询示例
     */
    private void executeJqListQuery() {
        log.info("=== 警情列表查询示例 ===");
        
        try {
            ReqJQStaQuery params = new ReqJQStaQuery();
            params.setBjdhsj_time_start("2024-01-01 00:00:00");
            params.setBjdhsj_time_end("2024-12-31 23:59:59");
            params.setPage(1);
            params.setLimit(10);
            
            var result = policeCaseEsService.queryJqList(params, "test_unit");
            
            log.info("警情列表查询结果数量: {}", result.getList().size());
            log.info("总记录数: {}", result.getTotal());
            
            for (RespSjryJqList jq : result.getList()) {
                log.info("警情编号: {}, 报警时间: {}, 处警类别: {}", 
                        jq.getJjbh(), jq.getBjdhsj_time(), jq.getCjlb());
            }
            
        } catch (Exception e) {
            log.error("警情列表查询示例失败", e);
        }
    }
}
