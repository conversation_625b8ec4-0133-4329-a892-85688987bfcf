package com.hl.test.es.example;

import com.hl.test.es.domain.ViewJqEsData;
import com.hl.test.es.service.ViewJqDataSyncService;
import com.hl.test.es.service.ViewJqEsDataService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * ViewJqEsData 数据同步使用示例
 * 演示如何使用数据同步服务
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ViewJqDataSyncExample implements CommandLineRunner {

    private final ViewJqEsDataService viewJqEsDataService;
    private final ViewJqDataSyncService syncService;

    @Override
    public void run(String... args) throws Exception {
        // 在实际环境中，这个示例不应该自动运行
        // 这里仅作为演示代码
        if (!"example".equals(System.getProperty("run.example"))) {
            return;
        }

        log.info("=== ViewJqEsData 数据同步示例开始 ===");

        // 示例1：全量同步
        executeFullSyncExample();

        // 示例2：增量同步
        executeIncrementalSyncExample();

        // 示例3：单个数据同步
        executeSingleSyncExample();

        // 示例4：批量同步
        executeBatchSyncExample();

        // 示例5：数据变更监听
        executeDataChangeExample();

        log.info("=== ViewJqEsData 数据同步示例结束 ===");
    }

    /**
     * 全量同步示例
     */
    private void executeFullSyncExample() {
        log.info("=== 全量同步示例 ===");
        
        try {
            ViewJqDataSyncService.SyncResult result = syncService.fullSync();
            
            log.info("全量同步结果:");
            log.info("- 状态: {}", result.getStatus());
            log.info("- 成功数量: {}", result.getSuccessCount());
            log.info("- 失败数量: {}", result.getFailedCount());
            log.info("- 开始时间: {}", result.getStartTime());
            log.info("- 结束时间: {}", result.getEndTime());
            
            if (result.getErrorMessage() != null) {
                log.error("- 错误信息: {}", result.getErrorMessage());
            }
            
        } catch (Exception e) {
            log.error("全量同步示例执行失败", e);
        }
    }

    /**
     * 增量同步示例
     */
    private void executeIncrementalSyncExample() {
        log.info("=== 增量同步示例 ===");
        
        try {
            String startTime = "2024-01-01 00:00:00";
            String endTime = "2024-01-01 23:59:59";
            
            ViewJqDataSyncService.SyncResult result = syncService.incrementalSync(startTime, endTime);
            
            log.info("增量同步结果 ({} - {}):", startTime, endTime);
            log.info("- 状态: {}", result.getStatus());
            log.info("- 成功数量: {}", result.getSuccessCount());
            log.info("- 失败数量: {}", result.getFailedCount());
            
        } catch (Exception e) {
            log.error("增量同步示例执行失败", e);
        }
    }

    /**
     * 单个数据同步示例
     */
    private void executeSingleSyncExample() {
        log.info("=== 单个数据同步示例 ===");
        
        try {
            String jjbh = "TEST_JJBH_001";
            
            // 同步方式1：直接调用同步服务
            boolean success = syncService.syncSingleData(jjbh);
            log.info("直接同步结果: {}", success ? "成功" : "失败");
            
            // 同步方式2：通过Service的自动同步机制
            ViewJqEsData testData = createTestData(jjbh);
            boolean saveResult = viewJqEsDataService.save(testData);
            log.info("保存并自动同步结果: {}", saveResult ? "成功" : "失败");
            
        } catch (Exception e) {
            log.error("单个数据同步示例执行失败", e);
        }
    }

    /**
     * 批量同步示例
     */
    private void executeBatchSyncExample() {
        log.info("=== 批量同步示例 ===");
        
        try {
            List<String> jjbhList = Arrays.asList(
                "TEST_JJBH_001",
                "TEST_JJBH_002",
                "TEST_JJBH_003"
            );
            
            ViewJqDataSyncService.SyncResult result = syncService.batchSyncByJjbhList(jjbhList);
            
            log.info("批量同步结果:");
            log.info("- 状态: {}", result.getStatus());
            log.info("- 成功数量: {}", result.getSuccessCount());
            log.info("- 失败数量: {}", result.getFailedCount());
            
        } catch (Exception e) {
            log.error("批量同步示例执行失败", e);
        }
    }

    /**
     * 数据变更监听示例
     */
    private void executeDataChangeExample() {
        log.info("=== 数据变更监听示例 ===");
        
        try {
            String jjbh = "TEST_JJBH_CHANGE";
            
            // 创建测试数据
            ViewJqEsData testData = createTestData(jjbh);
            
            // 插入数据 - 会触发插入事件
            log.info("插入数据，将触发插入事件...");
            viewJqEsDataService.save(testData);
            
            // 等待异步处理
            Thread.sleep(1000);
            
            // 更新数据 - 会触发更新事件
            log.info("更新数据，将触发更新事件...");
            testData.setBjr("更新后的报警人");
            viewJqEsDataService.updateById(testData);
            
            // 等待异步处理
            Thread.sleep(1000);
            
            // 删除数据 - 会触发删除事件
            log.info("删除数据，将触发删除事件...");
            viewJqEsDataService.removeById(jjbh);
            
            // 等待异步处理
            Thread.sleep(1000);
            
            log.info("数据变更监听示例完成");
            
        } catch (Exception e) {
            log.error("数据变更监听示例执行失败", e);
        }
    }

    /**
     * 创建测试数据
     */
    private ViewJqEsData createTestData(String jjbh) {
        ViewJqEsData data = new ViewJqEsData();
        data.setJjbh(jjbh);
        data.setBjr("测试报警人");
        data.setBjlx("测试报警类型");
        data.setJjdwmc("测试接警单位");
        data.setLxdh("13800138000");
        data.setBjnr("这是一个测试警情");
        data.setSfdd("测试地点");
        data.setBjdhsjTime("2024-01-01 12:00:00");
        data.setCjsjTime("2024-01-01 12:30:00");
        data.setBzzt(0);
        data.setSpjg(-1);
        data.setMark(0);
        
        return data;
    }
}
