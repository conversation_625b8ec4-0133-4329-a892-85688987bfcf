# ViewJqEsData 到 WjJqAnalysisDocument 数据同步系统

## 概述

本系统提供了一个现代、优雅、解耦的数据同步解决方案，用于将 `ViewJqEsData` 数据自动同步到 Elasticsearch 的 `WjJqAnalysisDocument` 索引中。

## 核心特性

### 🚀 现代架构设计
- **事件驱动**: 基于 Spring Event 机制，实现数据变更的自动监听和同步
- **异步处理**: 使用线程池异步处理同步任务，不阻塞主业务流程
- **解耦设计**: 服务间职责清晰，易于维护和扩展

### 📊 多种同步方式
- **全量同步**: 一次性同步所有数据
- **增量同步**: 基于时间范围的增量数据同步
- **单个同步**: 针对特定数据的精确同步
- **批量同步**: 批量处理指定的数据列表
- **自动同步**: 数据变更时自动触发同步

### 🔧 灵活的配置
- **批量大小可配置**: 支持自定义批量处理大小
- **线程池可配置**: 支持自定义异步执行器配置
- **错误处理**: 完善的异常处理和日志记录

## 系统架构

```
ViewJqEsData (数据源)
       ↓
ViewJqDataConvertService (数据转换)
       ↓
ViewJqDataSyncService (同步服务)
       ↓
WjJqAnalysisDocument (ES目标)

事件监听机制:
ViewJqEsDataService → Event Publisher → ViewJqDataChangeListener → 异步同步
```

## 核心组件

### 1. 数据转换服务 (ViewJqDataConvertService)
负责将 `ViewJqEsData` 转换为 `WjJqAnalysisDocument`：
- 字段映射和类型转换
- 时间格式处理
- 数据清洗和验证

### 2. 数据同步服务 (ViewJqDataSyncService)
提供各种同步功能：
- 全量同步
- 增量同步
- 单个数据同步
- 批量同步

### 3. 数据变更监听 (ViewJqDataChangeListener)
监听数据变更事件：
- 插入事件监听
- 更新事件监听
- 删除事件监听

### 4. 增强的数据服务 (ViewJqEsDataService)
集成了自动同步功能的数据服务：
- 保存时自动同步
- 更新时自动同步
- 删除时自动同步

## 使用方式

### 1. 自动同步（推荐）

当你使用 `ViewJqEsDataService` 进行数据操作时，系统会自动触发同步：

```java
@Autowired
private ViewJqEsDataService viewJqEsDataService;

// 保存数据 - 自动同步到ES
ViewJqEsData data = new ViewJqEsData();
data.setJjbh("JJ202401010001");
// ... 设置其他字段
viewJqEsDataService.save(data);

// 更新数据 - 自动同步到ES
data.setBjr("更新后的报警人");
viewJqEsDataService.updateById(data);

// 删除数据 - 自动从ES删除
viewJqEsDataService.removeById("JJ202401010001");
```

### 2. 手动同步

#### 全量同步
```java
@Autowired
private ViewJqDataSyncService syncService;

// 全量同步所有数据
ViewJqDataSyncService.SyncResult result = syncService.fullSync();
System.out.println("同步完成，处理了 " + result.getSuccessCount() + " 条数据");
```

#### 增量同步
```java
// 同步指定时间范围的数据
String startTime = "2024-01-01 00:00:00";
String endTime = "2024-01-01 23:59:59";
ViewJqDataSyncService.SyncResult result = syncService.incrementalSync(startTime, endTime);
```

#### 单个数据同步
```java
// 同步单个数据
boolean success = syncService.syncSingleData("JJ202401010001");

// 异步同步单个数据
CompletableFuture<Boolean> future = syncService.syncSingleDataAsync("JJ202401010001");
```

#### 批量同步
```java
// 批量同步指定的接警编号列表
List<String> jjbhList = Arrays.asList("JJ001", "JJ002", "JJ003");
ViewJqDataSyncService.SyncResult result = syncService.batchSyncByJjbhList(jjbhList);
```

### 3. REST API 调用

系统提供了完整的 REST API 接口：

```bash
# 全量同步
curl -X POST "http://localhost:8080/api/viewjq-sync/full"

# 增量同步
curl -X POST "http://localhost:8080/api/viewjq-sync/incremental?startTime=2024-01-01 00:00:00&endTime=2024-01-01 23:59:59"

# 单个数据同步
curl -X POST "http://localhost:8080/api/viewjq-sync/single?jjbh=JJ202401010001"

# 批量同步
curl -X POST "http://localhost:8080/api/viewjq-sync/batch" \
  -H "Content-Type: application/json" \
  -d '["JJ001", "JJ002", "JJ003"]'

# 删除ES数据
curl -X DELETE "http://localhost:8080/api/viewjq-sync/delete?jjbh=JJ202401010001"

# 健康检查
curl -X GET "http://localhost:8080/api/viewjq-sync/health"

# 获取统计信息
curl -X GET "http://localhost:8080/api/viewjq-sync/stats"
```

## 配置说明

### 异步执行器配置

系统使用两个异步执行器：

1. **数据同步执行器** (`viewJqSyncExecutor`)
   - 核心线程数: 4
   - 最大线程数: 8
   - 队列容量: 200

2. **事件监听执行器** (`viewJqEventExecutor`)
   - 核心线程数: 2
   - 最大线程数: 4
   - 队列容量: 100

### 批量处理配置

- 默认批量大小: 1000
- 可通过修改 `ViewJqDataSyncService.BATCH_SIZE` 常量调整

## 监控和日志

### 日志级别
- INFO: 同步操作的开始、结束和结果
- DEBUG: 详细的数据转换过程
- WARN: 数据格式问题或轻微错误
- ERROR: 同步失败或严重错误

### 关键日志示例
```
2024-01-01 12:00:00 INFO  - 开始全量同步 ViewJqEsData 到 ES
2024-01-01 12:00:01 INFO  - 已同步第 1 页，本页 1000 条，累计 1000 条
2024-01-01 12:00:05 INFO  - 全量同步完成，共处理 5000 条数据
2024-01-01 12:01:00 INFO  - 监听到数据插入事件: JJ202401010001
2024-01-01 12:01:01 INFO  - 数据插入同步成功: JJ202401010001
```

## 性能优化建议

### 1. 批量操作优先
- 对于大量数据，优先使用批量同步而非逐个同步
- 合理设置批量大小，避免内存溢出

### 2. 异步处理
- 利用异步同步功能，避免阻塞主业务流程
- 监控异步任务队列，防止积压

### 3. 增量同步
- 定期使用增量同步，减少全量同步频率
- 根据业务需求设置合适的同步时间间隔

### 4. 错误处理
- 监控同步失败的数据，及时处理异常情况
- 建立重试机制，提高同步成功率

## 扩展点

### 1. 自定义数据转换
继承 `ViewJqDataConvertService` 并重写转换方法：

```java
@Service
public class CustomViewJqDataConvertService extends ViewJqDataConvertService {
    @Override
    public WjJqAnalysisDocument convertToDocument(ViewJqEsData source) {
        WjJqAnalysisDocument document = super.convertToDocument(source);
        // 添加自定义转换逻辑
        return document;
    }
}
```

### 2. 自定义事件监听
实现自定义的事件监听器：

```java
@Component
public class CustomDataChangeListener {
    @EventListener
    public void handleCustomEvent(ViewJqDataChangeListener.ViewJqDataUpdateEvent event) {
        // 自定义处理逻辑
    }
}
```

### 3. 同步策略扩展
继承 `ViewJqDataSyncService` 并添加新的同步策略：

```java
@Service
public class ExtendedSyncService extends ViewJqDataSyncService {
    public SyncResult customSync(CustomSyncParams params) {
        // 实现自定义同步逻辑
    }
}
```

## 故障排查

### 常见问题

1. **同步失败**
   - 检查ES连接状态
   - 查看错误日志
   - 验证数据格式

2. **性能问题**
   - 调整批量大小
   - 优化线程池配置
   - 检查ES集群性能

3. **数据不一致**
   - 执行全量同步
   - 检查数据转换逻辑
   - 验证ES索引映射

### 调试技巧

1. 启用DEBUG日志查看详细过程
2. 使用单个数据同步测试转换逻辑
3. 监控ES集群状态和性能指标
4. 检查异步任务队列状态

## 总结

本数据同步系统提供了完整、灵活、高性能的数据同步解决方案。通过事件驱动和异步处理，实现了数据的实时同步，同时保持了系统的高可用性和可扩展性。
