package com.hl.test.es.controller;


import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.google.gson.Gson;
import com.hl.common.domain.R;
import com.hl.security.User;
import com.hl.security.UserUtils;
import com.hl.test.domain.req.ReqJQStaQuery;
import com.hl.test.domain.resp.RespJQSta;
import com.hl.test.domain.resp.RespSjryBq;
import com.hl.test.domain.resp.RespSjryJqList;
import com.hl.test.es.service.PoliceCaseDataSyncService;
import com.hl.test.es.service.PoliceCaseEsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.dromara.easyes.core.biz.EsPageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 警情统计ES优化版Controller
 * 基于Elasticsearch提供高性能的警情统计查询服务
 */
@Slf4j
@RestController
@RequestMapping("/api/jq-sta-es")
@Api(tags = "警情统计ES优化版")
public class JQStaEsController {
    
    @Autowired
    private PoliceCaseEsService policeCaseEsService;
    
    @Autowired
    private PoliceCaseDataSyncService dataSyncService;
    
    /**
     * 警情统计（ES优化版）
     */
    @PostMapping("/query")
    @ApiOperation(value = "警情统计-ES优化版")
    public R<List<RespJQSta>> queryEs(@Valid @RequestBody ReqJQStaQuery params) {
        try {
            log.info("ES警情统计查询: {}", params);
            
            User user = UserUtils.getUserNull();
            JSONArray units = user.getOrganization();
            JSONObject u = units.getJSONObject(0);
            String unit = u.getString("organization_id");
            
            List<RespJQSta> result = policeCaseEsService.queryJqSta(params, unit);
            
            log.info("ES警情统计查询完成，返回 {} 条结果", result.size());
            return R.ok(result);
            
        } catch (Exception e) {
            log.error("ES警情统计查询失败", e);
            return R.fail("查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 涉警人员统计（ES优化版）
     */
    @PostMapping("/query_sjry")
    @ApiOperation(value = "涉警人员统计-ES优化版")
    public R<List<RespJQSta>> querySjryEs(@Valid @RequestBody ReqJQStaQuery params) {
        try {
            log.info("ES涉警人员统计查询: {}", params);
            
            User user = UserUtils.getUserNull();
            JSONArray units = user.getOrganization();
            JSONObject u = units.getJSONObject(0);
            String unit = u.getString("organization_id");
            
            List<RespJQSta> result = policeCaseEsService.queryJqStaSjry(params, unit);
            
            log.info("ES涉警人员统计查询完成，返回 {} 条结果", result.size());
            return R.ok(result);
            
        } catch (Exception e) {
            log.error("ES涉警人员统计查询失败", e);
            return R.fail("查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 警情统计明细（ES优化版）
     */
    @PostMapping("/sta_det")
    @ApiOperation(value = "警情统计明细-ES优化版")
    public R<RespSjryBq> staDetEs(@Valid @RequestBody ReqJQStaQuery params) {
        try {
            log.info("ES警情统计明细查询: {}", params);
            
            if (StringUtils.isBlank(params.getLabel())) {
                return R.fail("label参数不能为空");
            }
            
            User user = UserUtils.getUserNull();
            JSONArray units = user.getOrganization();
            JSONObject u = units.getJSONObject(0);
            String unit = u.getString("organization_id");
            
            RespSjryBq result = policeCaseEsService.queryStaDet(params, unit, params.getLabel());
            
            log.info("ES警情统计明细查询完成");
            return R.ok(result);
            
        } catch (Exception e) {
            log.error("ES警情统计明细查询失败", e);
            return R.fail("查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 警情统计明细列表（ES优化版）
     */
    @PostMapping("/sta_jq_list")
    @ApiOperation(value = "警情统计明细列表-ES优化版")
    public R<List<RespSjryJqList>> staJqListEs(@Valid @RequestBody ReqJQStaQuery params) {
        try {
            log.info("ES警情列表查询: {}", params);
            
            User user = UserUtils.getUserNull();
            JSONArray units = user.getOrganization();
            JSONObject u = units.getJSONObject(0);
            String unit = u.getString("organization_id");
            
            EsPageInfo<RespSjryJqList> pageInfo = policeCaseEsService.queryJqList(params, unit);
            
            log.info("ES警情列表查询完成，返回 {} 条结果", pageInfo.getList().size());
            return R.ok(pageInfo.getList(), pageInfo.getTotal().intValue());
            
        } catch (Exception e) {
            log.error("ES警情列表查询失败", e);
            return R.fail("查询失败: " + e.getMessage());
        }
    }
    
    // === 数据同步管理接口 ===
    
    /**
     * 全量同步数据到ES
     */
    @PostMapping("/sync/full")
    @ApiOperation(value = "全量同步数据到ES")
    public R<String> fullSync() {
        try {
            log.info("开始全量同步数据到ES");
            
            // 检查索引是否存在，不存在则创建
            if (!dataSyncService.indexExists()) {
                dataSyncService.createIndex();
            }
            
            dataSyncService.fullSync();
            
            return R.ok("全量同步完成");
            
        } catch (Exception e) {
            log.error("全量同步失败", e);
            return R.fail("全量同步失败: " + e.getMessage());
        }
    }
    
    /**
     * 增量同步数据到ES
     */
    @PostMapping("/sync/incremental")
    @ApiOperation(value = "增量同步数据到ES")
    public R<String> incrementalSync(@RequestParam String startTime, @RequestParam String endTime) {
        try {
            log.info("开始增量同步数据到ES: {} - {}", startTime, endTime);
            
            dataSyncService.incrementalSync(startTime, endTime);
            
            return R.ok("增量同步完成");
            
        } catch (Exception e) {
            log.error("增量同步失败", e);
            return R.fail("增量同步失败: " + e.getMessage());
        }
    }
    
    /**
     * 同步单个警情到ES
     */
    @PostMapping("/sync/single")
    @ApiOperation(value = "同步单个警情到ES")
    public R<String> syncSingle(@RequestParam String jjbh) {
        try {
            log.info("同步单个警情到ES: {}", jjbh);
            
            dataSyncService.syncSingleCase(jjbh);
            
            return R.ok("同步完成");
            
        } catch (Exception e) {
            log.error("同步单个警情失败", e);
            return R.fail("同步失败: " + e.getMessage());
        }
    }
    
    /**
     * 创建ES索引
     */
    @PostMapping("/index/create")
    @ApiOperation(value = "创建ES索引")
    public R<String> createIndex() {
        try {
            boolean success = dataSyncService.createIndex();
            return success ? R.ok("索引创建成功") : R.fail("索引创建失败");
        } catch (Exception e) {
            log.error("创建索引失败", e);
            return R.fail("创建索引失败: " + e.getMessage());
        }
    }
    
    /**
     * 删除ES索引
     */
    @PostMapping("/index/delete")
    @ApiOperation(value = "删除ES索引")
    public R<String> deleteIndex() {
        try {
            boolean success = dataSyncService.deleteIndex();
            return success ? R.ok("索引删除成功") : R.fail("索引删除失败");
        } catch (Exception e) {
            log.error("删除索引失败", e);
            return R.fail("删除索引失败: " + e.getMessage());
        }
    }
    
    /**
     * 检查ES索引状态
     */
    @GetMapping("/index/status")
    @ApiOperation(value = "检查ES索引状态")
    public R<JSONObject> indexStatus() {
        try {
            JSONObject status = new JSONObject();
            status.put("exists", dataSyncService.indexExists());
            
            return R.ok(status);
            
        } catch (Exception e) {
            log.error("检查索引状态失败", e);
            return R.fail("检查索引状态失败: " + e.getMessage());
        }
    }
}
