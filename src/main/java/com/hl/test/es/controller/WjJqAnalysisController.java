package com.hl.test.es.controller;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.hl.common.domain.R;
import com.hl.security.User;
import com.hl.security.UserUtils;
import com.hl.test.domain.req.ReqJQStaQuery;
import com.hl.test.domain.resp.RespJQSta;
import com.hl.test.domain.resp.RespSjryBq;
import com.hl.test.domain.resp.RespSjryJqList;
import com.hl.test.es.service.WjJqAnalysisQueryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 基于 WjJqAnalysisDocument 的警情统计查询控制器
 * 使用 Easy-ES 优化原有的警情统计接口
 */
@Slf4j
@RestController
@RequestMapping("/api/wj-jq-analysis")
@Api(tags = "警情分析统计-ES优化版")
@RequiredArgsConstructor
public class WjJqAnalysisController {

    private final WjJqAnalysisQueryService queryService;

    /**
     * 警情统计查询（ES优化版）
     * 对应原 JQStaController.query 接口
     */
    @PostMapping("/query")
    @ApiOperation(value = "警情统计查询-ES优化版")
    public R<List<RespJQSta>> query(@Valid @RequestBody ReqJQStaQuery params) {
        try {
            log.info("ES警情统计查询开始: {}", params);
            
            // 获取用户单位信息
            String userUnit = getUserUnit();
            
            // 执行ES查询
            List<RespJQSta> result = queryService.queryJqStatistics(params, userUnit);
            
            log.info("ES警情统计查询完成，返回 {} 个标签类型的统计结果", result.size());
            return R.ok(result);
            
        } catch (Exception e) {
            log.error("ES警情统计查询失败", e);
            return R.fail("查询失败: " + e.getMessage());
        }
    }

    /**
     * 涉警人员统计查询（ES优化版）
     * 对应原 JQStaController.query_sjry 接口
     */
    @PostMapping("/query_sjry")
    @ApiOperation(value = "涉警人员统计查询-ES优化版")
    public R<List<RespJQSta>> querySjry(@Valid @RequestBody ReqJQStaQuery params) {
        try {
            log.info("ES涉警人员统计查询开始: {}", params);
            
            // 获取用户单位信息
            String userUnit = getUserUnit();
            
            // 执行ES查询
            List<RespJQSta> result = queryService.queryInvolvedPersonStatistics(params, userUnit);
            
            log.info("ES涉警人员统计查询完成，返回 {} 个统计结果", result.size());
            return R.ok(result);
            
        } catch (Exception e) {
            log.error("ES涉警人员统计查询失败", e);
            return R.fail("查询失败: " + e.getMessage());
        }
    }

    /**
     * 警情统计明细查询（ES优化版）
     * 对应原 JQStaController.sta_det 接口
     */
    @PostMapping("/sta_det")
    @ApiOperation(value = "警情统计明细查询-ES优化版")
    public R<RespSjryBq> staDet(@Valid @RequestBody ReqJQStaQuery params) {
        try {
            log.info("ES警情统计明细查询开始: {}", params);
            
            // 验证必需参数
            if (params.getLabel() == null || params.getLabel().trim().isEmpty()) {
                return R.fail("label参数不能为空");
            }
            
            // 获取用户单位信息
            String userUnit = getUserUnit();
            
            // 执行ES查询
            RespSjryBq result = queryService.queryStatisticsDetail(params, userUnit);
            
            log.info("ES警情统计明细查询完成");
            return R.ok(result);
            
        } catch (Exception e) {
            log.error("ES警情统计明细查询失败", e);
            return R.fail("查询失败: " + e.getMessage());
        }
    }

    /**
     * 警情统计明细列表查询（ES优化版）
     * 对应原 JQStaController.sta_jq_list 接口
     */
    @PostMapping("/sta_jq_list")
    @ApiOperation(value = "警情统计明细列表查询-ES优化版")
    public R<List<RespSjryJqList>> staJqList(@Valid @RequestBody ReqJQStaQuery params) {
        try {
            log.info("ES警情统计明细列表查询开始: {}", params);
            
            // 获取用户单位信息
            String userUnit = getUserUnit();
            
            // 执行ES查询
            WjJqAnalysisQueryService.PageResult<RespSjryJqList> result = 
                queryService.queryStatisticsJqList(params, userUnit);
            
            log.info("ES警情统计明细列表查询完成，返回 {} 条记录，总计 {} 条", 
                    result.getList().size(), result.getTotal());
            return R.ok(result.getList(), result.getTotal().intValue());
            
        } catch (Exception e) {
            log.error("ES警情统计明细列表查询失败", e);
            return R.fail("查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据接警编号列表查询警情详情（ES优化版）
     */
    @PostMapping("/query_by_jjbh_list")
    @ApiOperation(value = "根据接警编号列表查询警情详情-ES优化版")
    public R<List<RespSjryJqList>> queryByJjbhList(@RequestBody List<String> jjbhList) {
        try {
            log.info("ES根据接警编号列表查询开始，数量: {}", jjbhList.size());
            
            // 获取用户单位信息
            String userUnit = getUserUnit();
            
            // 执行ES查询
            List<RespSjryJqList> result = queryService.queryByJjbhList(jjbhList, userUnit);
            
            log.info("ES根据接警编号列表查询完成，返回 {} 条记录", result.size());
            return R.ok(result);
            
        } catch (Exception e) {
            log.error("ES根据接警编号列表查询失败", e);
            return R.fail("查询失败: " + e.getMessage());
        }
    }

    /**
     * 标签聚合统计查询（ES优化版）
     */
    @PostMapping("/label_aggregation")
    @ApiOperation(value = "标签聚合统计查询-ES优化版")
    public R<JSONObject> labelAggregation(@Valid @RequestBody ReqJQStaQuery params) {
        try {
            log.info("ES标签聚合统计查询开始: {}", params);
            
            // 获取用户单位信息
            String userUnit = getUserUnit();
            
            // 执行ES聚合查询
            JSONObject result = queryService.queryLabelAggregation(params, userUnit);
            
            log.info("ES标签聚合统计查询完成");
            return R.ok(result);
            
        } catch (Exception e) {
            log.error("ES标签聚合统计查询失败", e);
            return R.fail("查询失败: " + e.getMessage());
        }
    }

    /**
     * 时间维度统计查询（ES优化版）
     */
    @PostMapping("/time_statistics")
    @ApiOperation(value = "时间维度统计查询-ES优化版")
    public R<JSONObject> timeStatistics(@Valid @RequestBody ReqJQStaQuery params) {
        try {
            log.info("ES时间维度统计查询开始: {}", params);
            
            // 获取用户单位信息
            String userUnit = getUserUnit();
            
            // 执行ES时间统计查询
            JSONObject result = queryService.queryTimeStatistics(params, userUnit);
            
            log.info("ES时间维度统计查询完成");
            return R.ok(result);
            
        } catch (Exception e) {
            log.error("ES时间维度统计查询失败", e);
            return R.fail("查询失败: " + e.getMessage());
        }
    }

    /**
     * 地理位置统计查询（ES优化版）
     */
    @PostMapping("/geo_statistics")
    @ApiOperation(value = "地理位置统计查询-ES优化版")
    public R<JSONObject> geoStatistics(@Valid @RequestBody ReqJQStaQuery params) {
        try {
            log.info("ES地理位置统计查询开始: {}", params);
            
            // 获取用户单位信息
            String userUnit = getUserUnit();
            
            // 执行ES地理统计查询
            JSONObject result = queryService.queryGeoStatistics(params, userUnit);
            
            log.info("ES地理位置统计查询完成");
            return R.ok(result);
            
        } catch (Exception e) {
            log.error("ES地理位置统计查询失败", e);
            return R.fail("查询失败: " + e.getMessage());
        }
    }

    /**
     * 获取查询性能统计
     */
    @GetMapping("/performance_stats")
    @ApiOperation(value = "获取查询性能统计")
    public R<JSONObject> getPerformanceStats() {
        try {
            JSONObject stats = queryService.getPerformanceStats();
            return R.ok(stats);
        } catch (Exception e) {
            log.error("获取性能统计失败", e);
            return R.fail("获取性能统计失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户单位信息
     */
    private String getUserUnit() {
        try {
            User user = UserUtils.getUserNull();
            JSONArray units = user.getOrganization();
            JSONObject u = units.getJSONObject(0);
            return u.getString("organization_id");
        } catch (Exception e) {
            log.warn("获取用户单位信息失败，使用默认值", e);
            return "default_unit";
        }
    }
}
