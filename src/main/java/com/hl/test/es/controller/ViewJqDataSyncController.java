package com.hl.test.es.controller;

import com.hl.common.domain.R;
import com.hl.test.es.service.ViewJqDataSyncService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * ViewJqEsData 到 ES 数据同步管理控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/viewjq-sync")
@Api(tags = "ViewJqEsData数据同步管理")
@RequiredArgsConstructor
public class ViewJqDataSyncController {

    private final ViewJqDataSyncService syncService;

    /**
     * 全量同步数据到ES
     */
    @PostMapping("/full")
    @ApiOperation(value = "全量同步数据到ES")
    public R<ViewJqDataSyncService.SyncResult> fullSync() {
        try {
            log.info("开始执行全量同步");
            ViewJqDataSyncService.SyncResult result = syncService.fullSync();
            
            if (result.getStatus() == ViewJqDataSyncService.SyncStatus.SUCCESS) {
                return R.ok(result, "全量同步完成");
            } else {
                return R.fail("全量同步失败: " + result.getErrorMessage());
            }
        } catch (Exception e) {
            log.error("全量同步接口调用失败", e);
            return R.fail("全量同步失败: " + e.getMessage());
        }
    }

    /**
     * 增量同步数据到ES
     */
    @PostMapping("/incremental")
    @ApiOperation(value = "增量同步数据到ES")
    public R<ViewJqDataSyncService.SyncResult> incrementalSync(
            @ApiParam(value = "开始时间", example = "2024-01-01 00:00:00") 
            @RequestParam(required = false) String startTime,
            @ApiParam(value = "结束时间", example = "2024-01-01 23:59:59") 
            @RequestParam(required = false) String endTime) {
        
        try {
            log.info("开始执行增量同步: {} - {}", startTime, endTime);
            ViewJqDataSyncService.SyncResult result = syncService.incrementalSync(startTime, endTime);
            
            if (result.getStatus() == ViewJqDataSyncService.SyncStatus.SUCCESS) {
                return R.ok(result, "增量同步完成");
            } else {
                return R.fail("增量同步失败: " + result.getErrorMessage());
            }
        } catch (Exception e) {
            log.error("增量同步接口调用失败", e);
            return R.fail("增量同步失败: " + e.getMessage());
        }
    }

    /**
     * 同步单个数据到ES
     */
    @PostMapping("/single")
    @ApiOperation(value = "同步单个数据到ES")
    public R<Boolean> syncSingle(
            @ApiParam(value = "接警编号", required = true) 
            @RequestParam String jjbh) {
        
        try {
            log.info("开始同步单个数据: {}", jjbh);
            boolean success = syncService.syncSingleData(jjbh);
            
            if (success) {
                return R.ok(true, "单个数据同步成功");
            } else {
                return R.fail("单个数据同步失败");
            }
        } catch (Exception e) {
            log.error("单个数据同步接口调用失败: {}", jjbh, e);
            return R.fail("单个数据同步失败: " + e.getMessage());
        }
    }

    /**
     * 异步同步单个数据到ES
     */
    @PostMapping("/single-async")
    @ApiOperation(value = "异步同步单个数据到ES")
    public R<String> syncSingleAsync(
            @ApiParam(value = "接警编号", required = true) 
            @RequestParam String jjbh) {
        
        try {
            log.info("开始异步同步单个数据: {}", jjbh);
            CompletableFuture<Boolean> future = syncService.syncSingleDataAsync(jjbh);
            
            // 这里可以返回任务ID，客户端可以通过任务ID查询同步状态
            return R.ok("async_task_" + System.currentTimeMillis(), "异步同步任务已提交");
            
        } catch (Exception e) {
            log.error("异步同步单个数据接口调用失败: {}", jjbh, e);
            return R.fail("异步同步任务提交失败: " + e.getMessage());
        }
    }

    /**
     * 批量同步指定的接警编号列表
     */
    @PostMapping("/batch")
    @ApiOperation(value = "批量同步指定数据到ES")
    public R<ViewJqDataSyncService.SyncResult> batchSync(
            @ApiParam(value = "接警编号列表", required = true) 
            @RequestBody List<String> jjbhList) {
        
        try {
            log.info("开始批量同步数据，数量: {}", jjbhList.size());
            ViewJqDataSyncService.SyncResult result = syncService.batchSyncByJjbhList(jjbhList);
            
            if (result.getStatus() == ViewJqDataSyncService.SyncStatus.SUCCESS) {
                return R.ok(result, "批量同步完成");
            } else {
                return R.fail("批量同步失败: " + result.getErrorMessage());
            }
        } catch (Exception e) {
            log.error("批量同步接口调用失败", e);
            return R.fail("批量同步失败: " + e.getMessage());
        }
    }

    /**
     * 从ES删除数据
     */
    @DeleteMapping("/delete")
    @ApiOperation(value = "从ES删除数据")
    public R<Boolean> deleteFromEs(
            @ApiParam(value = "接警编号", required = true) 
            @RequestParam String jjbh) {
        
        try {
            log.info("开始从ES删除数据: {}", jjbh);
            boolean success = syncService.deleteFromEs(jjbh);
            
            if (success) {
                return R.ok(true, "数据删除成功");
            } else {
                return R.fail("数据删除失败");
            }
        } catch (Exception e) {
            log.error("删除数据接口调用失败: {}", jjbh, e);
            return R.fail("数据删除失败: " + e.getMessage());
        }
    }

    /**
     * 检查ES连接状态
     */
    @GetMapping("/health")
    @ApiOperation(value = "检查ES连接状态")
    public R<String> checkHealth() {
        try {
            // 这里可以添加ES连接检查逻辑
            return R.ok("healthy", "ES连接正常");
        } catch (Exception e) {
            log.error("ES健康检查失败", e);
            return R.fail("ES连接异常: " + e.getMessage());
        }
    }

    /**
     * 获取同步统计信息
     */
    @GetMapping("/stats")
    @ApiOperation(value = "获取同步统计信息")
    public R<SyncStats> getSyncStats() {
        try {
            // 这里可以添加统计信息收集逻辑
            SyncStats stats = new SyncStats();
            stats.setTotalSynced(0L);
            stats.setLastSyncTime("暂无");
            stats.setStatus("正常");
            
            return R.ok(stats, "获取统计信息成功");
        } catch (Exception e) {
            log.error("获取同步统计信息失败", e);
            return R.fail("获取统计信息失败: " + e.getMessage());
        }
    }

    /**
     * 同步统计信息
     */
    public static class SyncStats {
        private Long totalSynced;
        private String lastSyncTime;
        private String status;

        // Getters and Setters
        public Long getTotalSynced() { return totalSynced; }
        public void setTotalSynced(Long totalSynced) { this.totalSynced = totalSynced; }
        
        public String getLastSyncTime() { return lastSyncTime; }
        public void setLastSyncTime(String lastSyncTime) { this.lastSyncTime = lastSyncTime; }
        
        public String getStatus() { return status; }
        public void setStatus(String status) { this.status = status; }
    }
}
