package com.hl.test.es.service;


import com.alibaba.fastjson2.JSONObject;
import com.hl.test.domain.req.ReqJQStaQuery;
import com.hl.test.domain.resp.*;
import com.hl.test.es.document.PoliceCaseAnalysisDocument;
import com.hl.test.es.mapper.PoliceCaseAnalysisMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.dromara.easyes.core.biz.EsPageInfo;
import org.dromara.easyes.core.conditions.select.LambdaEsQueryWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 警情ES查询服务
 * 提供基于ES的高性能查询功能
 */
@Slf4j
@Service
public class PoliceCaseEsService {
    
    @Autowired
    private PoliceCaseAnalysisMapper policeCaseMapper;
    
    private final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    
    /**
     * 警情统计查询（优化版）
     */
    public List<RespJQSta> queryJqSta(ReqJQStaQuery params, String unit) {
        try {
            LambdaEsQueryWrapper<PoliceCaseAnalysisDocument> wrapper = buildBaseQuery(params, unit);
            
            // 执行聚合查询，按标签分组统计
            Map<String, Object> aggregations = new HashMap<>();
            aggregations.put("label_stats", Map.of(
                "terms", Map.of(
                    "field", "labels.label.keyword",
                    "size", 1000
                ),
                "aggs", Map.of(
                    "type_group", Map.of(
                        "terms", Map.of("field", "labels.type")
                    ),
                    "unit_group", Map.of(
                        "terms", Map.of("field", "labels.unit.keyword")
                    )
                )
            ));
            
            // 这里需要使用原生ES查询来实现复杂聚合
            // Easy-ES的聚合功能相对简单，复杂聚合建议使用原生查询
            List<PoliceCaseAnalysisDocument> documents = policeCaseMapper.selectList(wrapper);
            
            // 手动进行分组统计
            return processJqStaResults(documents);
            
        } catch (Exception e) {
            log.error("ES警情统计查询失败", e);
            return new ArrayList<>();
        }
    }
    
    /**
     * 涉警人员统计查询（优化版）
     */
    public List<RespJQSta> queryJqStaSjry(ReqJQStaQuery params, String unit) {
        try {
            LambdaEsQueryWrapper<PoliceCaseAnalysisDocument> wrapper = buildBaseQuery(params, unit);
            
            // 只查询有涉警人员的记录
            wrapper.gt(PoliceCaseAnalysisDocument::getInvolvedPersonCount, 0);
            
            List<PoliceCaseAnalysisDocument> documents = policeCaseMapper.selectList(wrapper);
            
            // 统计涉警人员
            return processSjryStaResults(documents);
            
        } catch (Exception e) {
            log.error("ES涉警人员统计查询失败", e);
            return new ArrayList<>();
        }
    }
    
    /**
     * 警情统计明细查询（优化版）
     */
    public RespSjryBq queryStaDet(ReqJQStaQuery params, String unit, String label) {
        try {
            LambdaEsQueryWrapper<PoliceCaseAnalysisDocument> wrapper = buildBaseQuery(params, unit);
            
            // 添加标签过滤
            if (StringUtils.isNotBlank(label)) {
                wrapper.nested("labels", nestedWrapper -> 
                    nestedWrapper.eq("labels.label", label)
                );
            }
            
            List<PoliceCaseAnalysisDocument> documents = policeCaseMapper.selectList(wrapper);
            
            // 处理明细数据
            return processStaDetResults(documents, label);
            
        } catch (Exception e) {
            log.error("ES警情明细查询失败", e);
            return new RespSjryBq();
        }
    }
    
    /**
     * 警情列表查询（优化版）
     */
    public EsPageInfo<RespSjryJqList> queryJqList(ReqJQStaQuery params, String unit) {
        try {
            LambdaEsQueryWrapper<PoliceCaseAnalysisDocument> wrapper = buildBaseQuery(params, unit);
            
            // 分页查询
            int page = params.getPage() > 0 ? params.getPage() : 1;
            int limit = params.getLimit() > 0 ? params.getLimit() : 20;
            
            EsPageInfo<PoliceCaseAnalysisDocument> pageInfo = policeCaseMapper.pageQuery(wrapper, page, limit);
            
            // 转换结果
            List<RespSjryJqList> resultList = pageInfo.getList().stream()
                    .map(this::convertToJqList)
                    .collect(Collectors.toList());
            
            EsPageInfo<RespSjryJqList> result = new EsPageInfo<>();
            result.setList(resultList);
            result.setTotal(pageInfo.getTotal());
            result.setPages(pageInfo.getPages());
            result.setCurrent(pageInfo.getCurrent());
            result.setSize(pageInfo.getSize());
            
            return result;
            
        } catch (Exception e) {
            log.error("ES警情列表查询失败", e);
            return new EsPageInfo<>();
        }
    }
    
    /**
     * 构建基础查询条件
     */
    private LambdaEsQueryWrapper<PoliceCaseAnalysisDocument> buildBaseQuery(ReqJQStaQuery params, String unit) {
        LambdaEsQueryWrapper<PoliceCaseAnalysisDocument> wrapper = new LambdaEsQueryWrapper<>();
        
        // 单位过滤
        if (StringUtils.isNotBlank(unit)) {
            wrapper.eq(PoliceCaseAnalysisDocument::getJjdw, unit);
        }
        
        // 时间范围过滤
        if (StringUtils.isNotBlank(params.getBjdhsj_time_start())) {
            wrapper.ge(PoliceCaseAnalysisDocument::getBjdhsjTime, params.getBjdhsj_time_start());
        }
        if (StringUtils.isNotBlank(params.getBjdhsj_time_end())) {
            wrapper.le(PoliceCaseAnalysisDocument::getBjdhsjTime, params.getBjdhsj_time_end());
        }
        
        // 标注时间过滤
        if (StringUtils.isNotBlank(params.getBz_time_start())) {
            wrapper.ge(PoliceCaseAnalysisDocument::getBzTime, params.getBz_time_start());
        }
        if (StringUtils.isNotBlank(params.getBz_time_end())) {
            wrapper.le(PoliceCaseAnalysisDocument::getBzTime, params.getBz_time_end());
        }
        
        // 处警类别过滤
        if (params.getCjlb() != null && !params.getCjlb().isEmpty()) {
            wrapper.in(PoliceCaseAnalysisDocument::getCjlb, params.getCjlb());
        }
        
        // 警情标签过滤
        if (params.getJqbz() != null && !params.getJqbz().isEmpty()) {
            for (String jqbz : params.getJqbz()) {
                wrapper.match(PoliceCaseAnalysisDocument::getJqbz, jqbz);
            }
        }
        
        // 接警编号列表过滤
        if (params.getJjbh_list() != null && !params.getJjbh_list().isEmpty()) {
            wrapper.in(PoliceCaseAnalysisDocument::getJjbh, params.getJjbh_list());
        }
        
        return wrapper;
    }
    
    /**
     * 处理警情统计结果
     */
    private List<RespJQSta> processJqStaResults(List<PoliceCaseAnalysisDocument> documents) {
        Map<Integer, List<RespJQStaDet>> typeGroups = new HashMap<>();
        
        // 按标签分组统计
        Map<String, Map<String, Object>> labelStats = new HashMap<>();
        
        for (PoliceCaseAnalysisDocument doc : documents) {
            if (doc.getLabels() != null) {
                for (PoliceCaseAnalysisDocument.LabelInfo label : doc.getLabels()) {
                    String key = label.getLabel();
                    labelStats.computeIfAbsent(key, k -> new HashMap<>())
                            .merge("count", 1, (a, b) -> (Integer) a + (Integer) b);
                    labelStats.get(key).put("type", label.getType());
                    labelStats.get(key).put("dictName", label.getDictName());
                    labelStats.get(key).put("memo", label.getMemo());
                    labelStats.get(key).put("unit", label.getUnit());
                }
            }
        }
        
        // 转换为响应格式
        for (Map.Entry<String, Map<String, Object>> entry : labelStats.entrySet()) {
            Map<String, Object> stats = entry.getValue();
            Integer type = (Integer) stats.get("type");
            
            RespJQStaDet det = new RespJQStaDet();
            det.setLabel(entry.getKey());
            det.setCount((Integer) stats.get("count"));
            det.setDict_name((String) stats.get("dictName"));
            det.setMemo((String) stats.get("memo"));
            det.setUnit((String) stats.get("unit"));
            
            typeGroups.computeIfAbsent(type, k -> new ArrayList<>()).add(det);
        }
        
        // 构建最终结果
        List<RespJQSta> result = new ArrayList<>();
        for (Map.Entry<Integer, List<RespJQStaDet>> entry : typeGroups.entrySet()) {
            RespJQSta sta = new RespJQSta();
            sta.setType(entry.getKey());
            sta.setName("类型" + entry.getKey());
            sta.setDets(entry.getValue());
            result.add(sta);
        }
        
        return result;
    }
    
    /**
     * 处理涉警人员统计结果
     */
    private List<RespJQSta> processSjryStaResults(List<PoliceCaseAnalysisDocument> documents) {
        Map<String, Integer> personStats = new HashMap<>();
        Map<String, String> personNames = new HashMap<>();
        
        for (PoliceCaseAnalysisDocument doc : documents) {
            if (doc.getInvolvedPersons() != null) {
                for (PoliceCaseAnalysisDocument.InvolvedPerson person : doc.getInvolvedPersons()) {
                    String gmsfhm = person.getGmsfhm();
                    if (StringUtils.isNotBlank(gmsfhm)) {
                        personStats.merge(gmsfhm, 1, Integer::sum);
                        personNames.put(gmsfhm, person.getXm());
                    }
                }
            }
        }
        
        // 只保留涉警次数大于1的人员
        List<RespJQStaDet> dets = personStats.entrySet().stream()
                .filter(entry -> entry.getValue() > 1)
                .map(entry -> {
                    RespJQStaDet det = new RespJQStaDet();
                    det.setLabel(entry.getKey());
                    det.setCount(entry.getValue());
                    String name = personNames.get(entry.getKey());
                    det.setMemo(name + "(" + entry.getKey() + ")");
                    det.setDict_name(det.getMemo());
                    det.setUnit("");
                    return det;
                })
                .sorted((a, b) -> b.getCount() - a.getCount())
                .collect(Collectors.toList());
        
        RespJQSta result = new RespJQSta();
        result.setType(0);
        result.setName("涉警人员统计");
        result.setDets(dets);
        
        return Collections.singletonList(result);
    }
    
    /**
     * 处理统计明细结果
     */
    private RespSjryBq processStaDetResults(List<PoliceCaseAnalysisDocument> documents, String label) {
        RespSjryBq result = new RespSjryBq();
        
        // 这里需要根据具体的业务逻辑来处理
        // 暂时返回空对象，具体实现需要根据原有逻辑来完善
        
        return result;
    }
    
    /**
     * 转换为警情列表对象
     */
    private RespSjryJqList convertToJqList(PoliceCaseAnalysisDocument doc) {
        RespSjryJqList result = new RespSjryJqList();
        result.setJjbh(doc.getJjbh());
        result.setBjdhsj_time(doc.getBjdhsjTime() != null ? dateFormat.format(doc.getBjdhsjTime()) : null);
        result.setBjlx(doc.getBjlx());
        result.setCjlb(doc.getCjlb());
        result.setCjjg(doc.getCjjg());
        result.setBjr(doc.getBjr());
        result.setLxdh(doc.getLxdh());
        result.setCjdwmc(doc.getCjdwmc());
        result.setCljgnr(doc.getCljgnr());
        
        return result;
    }
}
