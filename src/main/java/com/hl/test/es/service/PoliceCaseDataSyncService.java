package com.hl.test.es.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.hl.test.domain.WjscJqCjxx;
import com.hl.test.domain.WjscJqJjxx;
import com.hl.test.domain.WjscJqSjxx;
import com.hl.test.es.document.PoliceCaseAnalysisDocument;
import com.hl.test.es.mapper.PoliceCaseAnalysisMapper;
import com.hl.test.mapper.WjscJqCjxxMapper;
import com.hl.test.mapper.WjscJqSjxxMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 警情数据同步服务
 * 负责将数据库数据同步到ES
 */
@Slf4j
@Service
public class PoliceCaseDataSyncService {
    
    @Autowired
    private PoliceCaseAnalysisMapper policeCaseMapper;
    
    @Autowired
    private PoliceCaseDataConvertService convertService;
    
    @Autowired
    private JdbcTemplate jdbcTemplate;
    
    @Autowired
    private WjscJqSjxxMapper wjscJqSjxxMapper;
    
    /**
     * 全量同步数据
     */
    @Transactional(readOnly = true)
    public void fullSync() {
        log.info("开始全量同步警情数据到ES");
        
        try {
            // 先删除现有索引数据
            policeCaseMapper.delete(null);
            
            // 分批查询并同步数据
            int batchSize = 1000;
            int offset = 0;
            
            while (true) {
                List<Map<String, Object>> batch = queryBatchData(offset, batchSize);
                if (batch.isEmpty()) {
                    break;
                }
                
                // 转换并保存到ES
                List<PoliceCaseAnalysisDocument> documents = convertService.convertToDocuments(batch);
                
                // 补充涉警人员信息
                enrichWithInvolvedPersons(documents);
                
                // 批量保存到ES
                policeCaseMapper.insertBatch(documents);
                
                log.info("已同步 {} 条数据", offset + batch.size());
                offset += batchSize;
            }
            
            log.info("全量同步完成");
            
        } catch (Exception e) {
            log.error("全量同步失败", e);
            throw new RuntimeException("全量同步失败", e);
        }
    }
    
    /**
     * 增量同步数据
     */
    @Transactional(readOnly = true)
    public void incrementalSync(String startTime, String endTime) {
        log.info("开始增量同步警情数据: {} - {}", startTime, endTime);
        
        try {
            String sql = buildIncrementalSql(startTime, endTime);
            List<Map<String, Object>> data = jdbcTemplate.queryForList(sql);
            
            if (!data.isEmpty()) {
                List<PoliceCaseAnalysisDocument> documents = convertService.convertToDocuments(data);
                enrichWithInvolvedPersons(documents);
                
                // 先删除已存在的文档，再插入新的
                for (PoliceCaseAnalysisDocument doc : documents) {
                    policeCaseMapper.deleteById(doc.getId());
                }
                
                policeCaseMapper.insertBatch(documents);
                log.info("增量同步完成，共处理 {} 条数据", documents.size());
            }
            
        } catch (Exception e) {
            log.error("增量同步失败", e);
            throw new RuntimeException("增量同步失败", e);
        }
    }
    
    /**
     * 同步单个警情
     */
    @Transactional(readOnly = true)
    public void syncSingleCase(String jjbh) {
        try {
            String sql = "SELECT * FROM v_jq_sta_new WHERE jjbh = ?";
            List<Map<String, Object>> data = jdbcTemplate.queryForList(sql, jjbh);
            
            if (!data.isEmpty()) {
                PoliceCaseAnalysisDocument document = convertService.convertToDocument(data.get(0));
                enrichWithInvolvedPersons(List.of(document));
                
                // 先删除再插入
                policeCaseMapper.deleteById(document.getId());
                policeCaseMapper.insert(document);
                
                log.info("同步单个警情完成: {}", jjbh);
            }
            
        } catch (Exception e) {
            log.error("同步单个警情失败: {}", jjbh, e);
        }
    }
    
    /**
     * 分批查询数据
     */
    private List<Map<String, Object>> queryBatchData(int offset, int batchSize) {
        String sql = """
            SELECT cj.jjbh, jj.bjdhsj_time, jj.bjlx, cj.cjlb, cj.cjjg, jj.bjr, jj.lxdh, 
                   cj.cjdwmc, cj.cljgnr, jj.jjdw, jj.jjdwmc, cj.jqbz, cj.fjbq, cj.cjjqbq,
                   cj.addressM, cj.personM, cj.resultM, cj.timeM, cj.toolM, cj.reasonM,
                   cj.bzzt, bz.spjg, bz.bz_time
            FROM wjsc_jq_cjxx cj 
            LEFT JOIN wjsc_jq_jjxx jj ON cj.jjbh = jj.jjbh
            LEFT JOIN jq_bz bz ON cj.jjbh = bz.jjbh
            ORDER BY cj.jjbh
            LIMIT ? OFFSET ?
            """;
        
        return jdbcTemplate.queryForList(sql, batchSize, offset);
    }
    
    /**
     * 构建增量同步SQL
     */
    private String buildIncrementalSql(String startTime, String endTime) {
        return String.format("""
            SELECT cj.jjbh, jj.bjdhsj_time, jj.bjlx, cj.cjlb, cj.cjjg, jj.bjr, jj.lxdh, 
                   cj.cjdwmc, cj.cljgnr, jj.jjdw, jj.jjdwmc, cj.jqbz, cj.fjbq, cj.cjjqbq,
                   cj.addressM, cj.personM, cj.resultM, cj.timeM, cj.toolM, cj.reasonM,
                   cj.bzzt, bz.spjg, bz.bz_time
            FROM wjsc_jq_cjxx cj 
            LEFT JOIN wjsc_jq_jjxx jj ON cj.jjbh = jj.jjbh
            LEFT JOIN jq_bz bz ON cj.jjbh = bz.jjbh
            WHERE (jj.bjdhsj_time BETWEEN '%s' AND '%s' 
                   OR bz.bz_time BETWEEN '%s' AND '%s'
                   OR cj.xgsj_time BETWEEN '%s' AND '%s')
            """, startTime, endTime, startTime, endTime, startTime, endTime);
    }
    
    /**
     * 补充涉警人员信息
     */
    private void enrichWithInvolvedPersons(List<PoliceCaseAnalysisDocument> documents) {
        if (documents.isEmpty()) {
            return;
        }
        
        // 获取所有警情编号
        List<String> jjbhs = documents.stream()
                .map(PoliceCaseAnalysisDocument::getJjbh)
                .collect(Collectors.toList());
        
        // 批量查询涉警人员信息
        QueryWrapper<WjscJqSjxx> wrapper = new QueryWrapper<>();
        wrapper.in("jjbh", jjbhs);
        List<WjscJqSjxx> sjxxList = wjscJqSjxxMapper.selectList(wrapper);
        
        // 按警情编号分组
        Map<String, List<WjscJqSjxx>> sjxxMap = sjxxList.stream()
                .collect(Collectors.groupingBy(WjscJqSjxx::getJjbh));
        
        // 为每个文档设置涉警人员信息
        for (PoliceCaseAnalysisDocument document : documents) {
            List<WjscJqSjxx> personList = sjxxMap.get(document.getJjbh());
            if (personList != null && !personList.isEmpty()) {
                convertService.setInvolvedPersons(document, personList);
            }
        }
    }
    
    /**
     * 检查ES索引是否存在
     */
    public boolean indexExists() {
        try {
            return policeCaseMapper.existsIndex();
        } catch (Exception e) {
            log.error("检查索引是否存在失败", e);
            return false;
        }
    }
    
    /**
     * 创建ES索引
     */
    public boolean createIndex() {
        try {
            return policeCaseMapper.createIndex();
        } catch (Exception e) {
            log.error("创建索引失败", e);
            return false;
        }
    }
    
    /**
     * 删除ES索引
     */
    public boolean deleteIndex() {
        try {
            return policeCaseMapper.deleteIndex();
        } catch (Exception e) {
            log.error("删除索引失败", e);
            return false;
        }
    }
}
