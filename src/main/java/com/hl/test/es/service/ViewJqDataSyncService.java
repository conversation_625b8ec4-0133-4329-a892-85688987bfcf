package com.hl.test.es.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.test.es.document.WjJqAnalysisDocument;
import com.hl.test.es.domain.ViewJqEsData;
import com.hl.test.es.esmapper.WjJqAnalysisDocumentMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;

/**
 * ViewJqEsData 到 ES 数据同步服务
 * 提供批量同步、增量同步、单个数据同步功能
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ViewJqDataSyncService {

    private final ViewJqEsDataService viewJqEsDataService;
    private final WjJqAnalysisDocumentMapper wjJqAnalysisDocumentMapper;
    private final ViewJqDataConvertService convertService;
    
    // 异步执行器
    private final Executor asyncExecutor = Executors.newFixedThreadPool(4);
    
    // 批量处理大小
    private static final int BATCH_SIZE = 1000;
    
    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    /**
     * 全量同步数据到ES
     * @return 同步结果统计
     */
    @Transactional(readOnly = true)
    public SyncResult fullSync() {
        log.info("开始全量同步 ViewJqEsData 到 ES");
        
        SyncResult result = new SyncResult();
        result.setStartTime(LocalDateTime.now());
        
        try {
            // 清空现有ES数据
            wjJqAnalysisDocumentMapper.delete(null);
            log.info("已清空ES中的现有数据");
            
            // 分页查询并同步
            int pageNum = 1;
            int totalProcessed = 0;
            
            while (true) {
                Page<ViewJqEsData> page = new Page<>(pageNum, BATCH_SIZE);
                IPage<ViewJqEsData> dataPage = viewJqEsDataService.page(page);
                
                if (CollectionUtils.isEmpty(dataPage.getRecords())) {
                    break;
                }
                
                // 转换并批量插入
                List<WjJqAnalysisDocument> documents = convertService.convertToDocuments(dataPage.getRecords());
                if (!CollectionUtils.isEmpty(documents)) {
                    wjJqAnalysisDocumentMapper.insertBatch(documents);
                    totalProcessed += documents.size();
                    
                    log.info("已同步第 {} 页，本页 {} 条，累计 {} 条", pageNum, documents.size(), totalProcessed);
                }
                
                pageNum++;
                
                // 防止内存溢出，每处理一定数量后稍作休息
                if (pageNum % 10 == 0) {
                    Thread.sleep(100);
                }
            }
            
            result.setSuccessCount(totalProcessed);
            result.setStatus(SyncStatus.SUCCESS);
            log.info("全量同步完成，共处理 {} 条数据", totalProcessed);
            
        } catch (Exception e) {
            result.setStatus(SyncStatus.FAILED);
            result.setErrorMessage(e.getMessage());
            log.error("全量同步失败", e);
            throw new RuntimeException("全量同步失败", e);
        } finally {
            result.setEndTime(LocalDateTime.now());
        }
        
        return result;
    }

    /**
     * 增量同步数据到ES
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 同步结果统计
     */
    @Transactional(readOnly = true)
    public SyncResult incrementalSync(String startTime, String endTime) {
        log.info("开始增量同步 ViewJqEsData 到 ES: {} - {}", startTime, endTime);
        
        SyncResult result = new SyncResult();
        result.setStartTime(LocalDateTime.now());
        
        try {
            // 构建查询条件
            LambdaQueryWrapper<ViewJqEsData> queryWrapper = new LambdaQueryWrapper<>();
            
            if (StringUtils.hasText(startTime)) {
                queryWrapper.ge(ViewJqEsData::getBjdhsjTime, startTime);
            }
            if (StringUtils.hasText(endTime)) {
                queryWrapper.le(ViewJqEsData::getBjdhsjTime, endTime);
            }
            
            // 分页查询并同步
            int pageNum = 1;
            int totalProcessed = 0;
            
            while (true) {
                Page<ViewJqEsData> page = new Page<>(pageNum, BATCH_SIZE);
                IPage<ViewJqEsData> dataPage = viewJqEsDataService.page(page, queryWrapper);
                
                if (CollectionUtils.isEmpty(dataPage.getRecords())) {
                    break;
                }
                
                // 转换数据
                List<WjJqAnalysisDocument> documents = convertService.convertToDocuments(dataPage.getRecords());
                
                if (!CollectionUtils.isEmpty(documents)) {
                    // 先删除已存在的文档，再插入新的
                    for (WjJqAnalysisDocument doc : documents) {
                        wjJqAnalysisDocumentMapper.deleteById(doc.getJjbh());
                    }
                    
                    wjJqAnalysisDocumentMapper.insertBatch(documents);
                    totalProcessed += documents.size();
                    
                    log.info("增量同步第 {} 页，本页 {} 条，累计 {} 条", pageNum, documents.size(), totalProcessed);
                }
                
                pageNum++;
            }
            
            result.setSuccessCount(totalProcessed);
            result.setStatus(SyncStatus.SUCCESS);
            log.info("增量同步完成，共处理 {} 条数据", totalProcessed);
            
        } catch (Exception e) {
            result.setStatus(SyncStatus.FAILED);
            result.setErrorMessage(e.getMessage());
            log.error("增量同步失败", e);
            throw new RuntimeException("增量同步失败", e);
        } finally {
            result.setEndTime(LocalDateTime.now());
        }
        
        return result;
    }

    /**
     * 同步单个数据到ES
     * @param jjbh 接警编号
     * @return 是否成功
     */
    @Transactional(readOnly = true)
    public boolean syncSingleData(String jjbh) {
        if (!StringUtils.hasText(jjbh)) {
            log.warn("接警编号为空，无法同步");
            return false;
        }
        
        try {
            log.info("开始同步单个数据到ES: {}", jjbh);
            
            // 查询源数据
            LambdaQueryWrapper<ViewJqEsData> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(ViewJqEsData::getJjbh, jjbh);
            ViewJqEsData sourceData = viewJqEsDataService.getOne(queryWrapper);
            
            if (sourceData == null) {
                log.warn("未找到接警编号为 {} 的数据", jjbh);
                return false;
            }
            
            // 转换数据
            WjJqAnalysisDocument document = convertService.convertToDocument(sourceData);
            
            if (document != null) {
                // 先删除已存在的文档，再插入新的
                wjJqAnalysisDocumentMapper.deleteById(jjbh);
                wjJqAnalysisDocumentMapper.insert(document);
                
                log.info("单个数据同步完成: {}", jjbh);
                return true;
            } else {
                log.error("数据转换失败: {}", jjbh);
                return false;
            }
            
        } catch (Exception e) {
            log.error("同步单个数据失败: {}", jjbh, e);
            return false;
        }
    }

    /**
     * 异步同步单个数据
     * @param jjbh 接警编号
     * @return CompletableFuture
     */
    public CompletableFuture<Boolean> syncSingleDataAsync(String jjbh) {
        return CompletableFuture.supplyAsync(() -> syncSingleData(jjbh), asyncExecutor);
    }

    /**
     * 批量同步指定的接警编号列表
     * @param jjbhList 接警编号列表
     * @return 同步结果统计
     */
    @Transactional(readOnly = true)
    public SyncResult batchSyncByJjbhList(List<String> jjbhList) {
        log.info("开始批量同步指定数据到ES，数量: {}", jjbhList.size());
        
        SyncResult result = new SyncResult();
        result.setStartTime(LocalDateTime.now());
        
        try {
            if (CollectionUtils.isEmpty(jjbhList)) {
                result.setStatus(SyncStatus.SUCCESS);
                result.setSuccessCount(0);
                return result;
            }
            
            // 查询源数据
            LambdaQueryWrapper<ViewJqEsData> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(ViewJqEsData::getJjbh, jjbhList);
            List<ViewJqEsData> sourceDataList = viewJqEsDataService.list(queryWrapper);
            
            if (!CollectionUtils.isEmpty(sourceDataList)) {
                // 转换数据
                List<WjJqAnalysisDocument> documents = convertService.convertToDocuments(sourceDataList);
                
                if (!CollectionUtils.isEmpty(documents)) {
                    // 先删除已存在的文档
                    for (WjJqAnalysisDocument doc : documents) {
                        wjJqAnalysisDocumentMapper.deleteById(doc.getJjbh());
                    }
                    
                    // 批量插入
                    wjJqAnalysisDocumentMapper.insertBatch(documents);
                    result.setSuccessCount(documents.size());
                }
            }
            
            result.setStatus(SyncStatus.SUCCESS);
            log.info("批量同步完成，共处理 {} 条数据", result.getSuccessCount());
            
        } catch (Exception e) {
            result.setStatus(SyncStatus.FAILED);
            result.setErrorMessage(e.getMessage());
            log.error("批量同步失败", e);
        } finally {
            result.setEndTime(LocalDateTime.now());
        }
        
        return result;
    }

    /**
     * 删除ES中的数据
     * @param jjbh 接警编号
     * @return 是否成功
     */
    public boolean deleteFromEs(String jjbh) {
        if (!StringUtils.hasText(jjbh)) {
            return false;
        }
        
        try {
            wjJqAnalysisDocumentMapper.deleteById(jjbh);
            log.info("已从ES删除数据: {}", jjbh);
            return true;
        } catch (Exception e) {
            log.error("从ES删除数据失败: {}", jjbh, e);
            return false;
        }
    }

    /**
     * 同步结果统计
     */
    public static class SyncResult {
        private LocalDateTime startTime;
        private LocalDateTime endTime;
        private int successCount = 0;
        private int failedCount = 0;
        private SyncStatus status;
        private String errorMessage;

        // Getters and Setters
        public LocalDateTime getStartTime() { return startTime; }
        public void setStartTime(LocalDateTime startTime) { this.startTime = startTime; }
        
        public LocalDateTime getEndTime() { return endTime; }
        public void setEndTime(LocalDateTime endTime) { this.endTime = endTime; }
        
        public int getSuccessCount() { return successCount; }
        public void setSuccessCount(int successCount) { this.successCount = successCount; }
        
        public int getFailedCount() { return failedCount; }
        public void setFailedCount(int failedCount) { this.failedCount = failedCount; }
        
        public SyncStatus getStatus() { return status; }
        public void setStatus(SyncStatus status) { this.status = status; }
        
        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
    }

    /**
     * 同步状态枚举
     */
    public enum SyncStatus {
        SUCCESS, FAILED, RUNNING
    }
}
