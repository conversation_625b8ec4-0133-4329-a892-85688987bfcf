package com.hl.test.es.service;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.google.gson.Gson;
import com.hl.test.Utils.RIUtil;
import com.hl.test.domain.WjscJqCjxx;
import com.hl.test.domain.WjscJqJjxx;
import com.hl.test.domain.WjscJqSjxx;
import com.hl.test.es.document.PoliceCaseAnalysisDocument;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 警情数据转换服务
 * 负责将数据库数据转换为ES文档对象
 */
@Slf4j
@Service
public class PoliceCaseDataConvertService {
    
    private final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    private final Gson gson = new Gson();
    
    /**
     * 将数据库查询结果转换为ES文档
     */
    public PoliceCaseAnalysisDocument convertToDocument(Map<String, Object> dbRecord) {
        PoliceCaseAnalysisDocument document = new PoliceCaseAnalysisDocument();
        
        try {
            // 基础信息
            document.setId(getString(dbRecord, "jjbh"));
            document.setJjbh(getString(dbRecord, "jjbh"));
            document.setBjdhsjTime(parseDate(getString(dbRecord, "bjdhsj_time")));
            document.setBzTime(parseDate(getString(dbRecord, "bz_time")));
            document.setJjdw(getString(dbRecord, "jjdw"));
            document.setJjdwmc(getString(dbRecord, "jjdwmc"));
            document.setBjlx(getString(dbRecord, "bjlx"));
            document.setCjlb(getString(dbRecord, "cjlb"));
            document.setCjjg(getString(dbRecord, "cjjg"));
            document.setBjr(getString(dbRecord, "bjr"));
            document.setLxdh(getString(dbRecord, "lxdh"));
            document.setCjdwmc(getString(dbRecord, "cjdwmc"));
            document.setCljgnr(getString(dbRecord, "cljgnr"));
            
            // 标签信息
            document.setJqbz(getString(dbRecord, "jqbz"));
            document.setFjbq(getString(dbRecord, "fjbq"));
            document.setCjjqbq(getString(dbRecord, "cjjqbq"));
            
            // 解析标签
            parseLabels(document, dbRecord);
            
            // 状态信息
            document.setBzzt(getInteger(dbRecord, "bzzt"));
            document.setSpjg(getInteger(dbRecord, "spjg"));
            
            // 计算统计字段
            calculateStatistics(document, dbRecord);
            
        } catch (Exception e) {
            log.error("转换ES文档失败: {}", e.getMessage(), e);
        }
        
        return document;
    }
    
    /**
     * 批量转换
     */
    public List<PoliceCaseAnalysisDocument> convertToDocuments(List<Map<String, Object>> dbRecords) {
        return dbRecords.stream()
                .map(this::convertToDocument)
                .collect(Collectors.toList());
    }
    
    /**
     * 解析标签信息
     */
    private void parseLabels(PoliceCaseAnalysisDocument document, Map<String, Object> dbRecord) {
        List<PoliceCaseAnalysisDocument.LabelInfo> labels = new ArrayList<>();
        
        // 解析各种标签
        String addressM = getString(dbRecord, "addressM");
        String personM = getString(dbRecord, "personM");
        String resultM = getString(dbRecord, "resultM");
        String timeM = getString(dbRecord, "timeM");
        String toolM = getString(dbRecord, "toolM");
        String reasonM = getString(dbRecord, "reasonM");
        
        // 设置合并后的标签文本
        document.setAddressLabels(cleanLabelText(addressM));
        document.setPersonLabels(cleanLabelText(personM));
        document.setResultLabels(cleanLabelText(resultM));
        document.setTimeLabels(cleanLabelText(timeM));
        document.setToolLabels(cleanLabelText(toolM));
        document.setReasonLabels(cleanLabelText(reasonM));
        
        // 解析标签详情（如果有label和type字段）
        String label = getString(dbRecord, "label");
        String dictName = getString(dbRecord, "dict_name");
        String memo = getString(dbRecord, "memo");
        Integer type = getInteger(dbRecord, "type");
        String unit = getString(dbRecord, "unit");
        
        if (StringUtils.isNotBlank(label)) {
            PoliceCaseAnalysisDocument.LabelInfo labelInfo = new PoliceCaseAnalysisDocument.LabelInfo();
            labelInfo.setLabel(label);
            labelInfo.setDictName(dictName);
            labelInfo.setMemo(memo);
            labelInfo.setType(type);
            labelInfo.setUnit(unit);
            labels.add(labelInfo);
        }
        
        document.setLabels(labels);
    }
    
    /**
     * 计算统计字段
     */
    private void calculateStatistics(PoliceCaseAnalysisDocument document, Map<String, Object> dbRecord) {
        // 计算单位层级
        String jjdw = document.getJjdw();
        if (StringUtils.isNotBlank(jjdw)) {
            document.setUnitLevel(calculateUnitLevel(jjdw));
        }
        
        // 计算时间范围标识
        if (document.getBjdhsjTime() != null) {
            document.setTimeRange(calculateTimeRange(document.getBjdhsjTime()));
        }
        
        // 初始化涉警人员相关字段
        document.setInvolvedPersonCount(0);
        document.setHasMultipleInvolved(false);
        document.setInvolvedPersons(new ArrayList<>());
    }
    
    /**
     * 设置涉警人员信息
     */
    public void setInvolvedPersons(PoliceCaseAnalysisDocument document, List<WjscJqSjxx> sjxxList) {
        if (sjxxList == null || sjxxList.isEmpty()) {
            return;
        }
        
        List<PoliceCaseAnalysisDocument.InvolvedPerson> involvedPersons = new ArrayList<>();
        
        for (WjscJqSjxx sjxx : sjxxList) {
            PoliceCaseAnalysisDocument.InvolvedPerson person = new PoliceCaseAnalysisDocument.InvolvedPerson();
            person.setGmsfhm(sjxx.getGmsfhm());
            person.setXm(sjxx.getXm());
            person.setSjlb(sjxx.getSjlb());
            person.setPersonLabels(cleanLabelText(sjxx.getPersonM()));
            // 涉警次数需要额外查询计算
            person.setInvolvedCount(1);
            
            involvedPersons.add(person);
        }
        
        document.setInvolvedPersons(involvedPersons);
        document.setInvolvedPersonCount(involvedPersons.size());
        document.setHasMultipleInvolved(involvedPersons.size() > 1);
    }
    
    /**
     * 清理标签文本
     */
    private String cleanLabelText(String labelText) {
        if (StringUtils.isBlank(labelText)) {
            return "";
        }
        
        try {
            // 尝试解析JSON数组
            JSONArray jsonArray = JSONArray.parseArray(labelText);
            return jsonArray.stream()
                    .map(Object::toString)
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.joining(" "));
        } catch (Exception e) {
            // 如果不是JSON格式，直接清理
            return labelText.replace("[", "")
                    .replace("]", "")
                    .replace("\"", "")
                    .replace(",", " ")
                    .trim();
        }
    }
    
    /**
     * 计算单位层级
     */
    private String calculateUnitLevel(String jjdw) {
        if (StringUtils.isBlank(jjdw)) {
            return "unknown";
        }
        
        try {
            JSONObject dict = RIUtil.dicts.get(jjdw);
            if (dict != null) {
                Integer type = dict.getInteger("type");
                if (type != null) {
                    switch (type) {
                        case 21:
                        case 22:
                            return "bureau"; // 市局
                        case 23:
                            return "branch"; // 分局
                        case 24:
                        case 25:
                            return "station"; // 派出所
                        default:
                            return "other";
                    }
                }
            }
        } catch (Exception e) {
            log.warn("计算单位层级失败: {}", jjdw);
        }
        
        return "unknown";
    }
    
    /**
     * 计算时间范围标识
     */
    private String calculateTimeRange(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        
        int year = cal.get(Calendar.YEAR);
        int month = cal.get(Calendar.MONTH) + 1;
        int quarter = (month - 1) / 3 + 1;
        
        return year + "-Q" + quarter;
    }
    
    /**
     * 解析日期
     */
    private Date parseDate(String dateStr) {
        if (StringUtils.isBlank(dateStr)) {
            return null;
        }
        
        try {
            return dateFormat.parse(dateStr);
        } catch (ParseException e) {
            log.warn("日期解析失败: {}", dateStr);
            return null;
        }
    }
    
    /**
     * 获取字符串值
     */
    private String getString(Map<String, Object> map, String key) {
        Object value = map.get(key);
        return value != null ? value.toString() : null;
    }
    
    /**
     * 获取整数值
     */
    private Integer getInteger(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value == null) {
            return null;
        }
        
        try {
            return Integer.valueOf(value.toString());
        } catch (NumberFormatException e) {
            return null;
        }
    }
}
