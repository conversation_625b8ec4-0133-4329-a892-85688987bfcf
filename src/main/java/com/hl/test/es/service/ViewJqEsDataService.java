package com.hl.test.es.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hl.test.es.document.WjJqAnalysisDocument;
import com.hl.test.es.domain.ViewJqEsData;
import com.hl.test.es.esmapper.WjJqAnalysisDocumentMapper;
import com.hl.test.es.mapper.ViewJqEsDataMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;

/**
 * ViewJqEsData 服务类
 * 集成了数据操作和ES同步功能
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ViewJqEsDataService extends ServiceImpl<ViewJqEsDataMapper, ViewJqEsData> {

    private final WjJqAnalysisDocumentMapper wjJqAnalysisDocumentMapper;
    private final ViewJqDataConvertService convertService;
    private final ApplicationEventPublisher eventPublisher;

    /**
     * 保存数据并同步到ES
     */
    @Override
    @Transactional
    public boolean save(ViewJqEsData entity) {
        boolean result = super.save(entity);
        if (result && StringUtils.hasText(entity.getJjbh())) {
            // 发布插入事件，异步同步到ES
            publishInsertEvent(entity.getJjbh(), entity);
        }
        return result;
    }

    /**
     * 批量保存数据并同步到ES
     */
    @Override
    @Transactional
    public boolean saveBatch(Collection<ViewJqEsData> entityList) {
        boolean result = super.saveBatch(entityList);
        if (result) {
            // 批量发布插入事件
            entityList.forEach(entity -> {
                if (StringUtils.hasText(entity.getJjbh())) {
                    publishInsertEvent(entity.getJjbh(), entity);
                }
            });
        }
        return result;
    }

    /**
     * 更新数据并同步到ES
     */
    @Override
    @Transactional
    public boolean updateById(ViewJqEsData entity) {
        // 获取更新前的数据
        ViewJqEsData oldData = null;
        if (entity.getJjbh() != null) {
            oldData = getById(entity.getJjbh());
        }

        boolean result = super.updateById(entity);
        if (result && StringUtils.hasText(entity.getJjbh())) {
            // 发布更新事件，异步同步到ES
            publishUpdateEvent(entity.getJjbh(), oldData, entity);
        }
        return result;
    }

    /**
     * 删除数据并从ES中删除
     */
    @Override
    @Transactional
    public boolean removeById(Serializable id) {
        // 获取删除前的数据
        ViewJqEsData data = getById(id);

        boolean result = super.removeById(id);
        if (result && data != null && StringUtils.hasText(data.getJjbh())) {
            // 发布删除事件，异步从ES删除
            publishDeleteEvent(data.getJjbh(), data);
        }
        return result;
    }

    /**
     * 根据条件删除数据并从ES中删除
     */
    @Override
    @Transactional
    public boolean remove(Wrapper<ViewJqEsData> queryWrapper) {
        // 先查询要删除的数据
        List<ViewJqEsData> dataList = list(queryWrapper);

        boolean result = super.remove(queryWrapper);
        if (result && dataList != null) {
            // 批量发布删除事件
            dataList.forEach(data -> {
                if (StringUtils.hasText(data.getJjbh())) {
                    publishDeleteEvent(data.getJjbh(), data);
                }
            });
        }
        return result;
    }

    /**
     * 手动同步单个数据到ES
     * @param jjbh 接警编号
     * @return 是否成功
     */
    public boolean syncToEs(String jjbh) {
        if (!StringUtils.hasText(jjbh)) {
            return false;
        }

        try {
            ViewJqEsData data = getById(jjbh);
            if (data == null) {
                log.warn("未找到接警编号为 {} 的数据", jjbh);
                return false;
            }

            WjJqAnalysisDocument document = convertService.convertToDocument(data);
            if (document != null) {
                // 先删除已存在的文档，再插入新的
                wjJqAnalysisDocumentMapper.deleteById(jjbh);
                wjJqAnalysisDocumentMapper.insert(document);

                log.info("手动同步数据到ES成功: {}", jjbh);
                return true;
            }
        } catch (Exception e) {
            log.error("手动同步数据到ES失败: {}", jjbh, e);
        }

        return false;
    }

    /**
     * 发布数据插入事件
     */
    private void publishInsertEvent(String jjbh, ViewJqEsData data) {
        try {
            ViewJqDataChangeListener.ViewJqDataInsertEvent event =
                new ViewJqDataChangeListener.ViewJqDataInsertEvent(jjbh, data);
            eventPublisher.publishEvent(event);
        } catch (Exception e) {
            log.error("发布数据插入事件失败: {}", jjbh, e);
        }
    }

    /**
     * 发布数据更新事件
     */
    private void publishUpdateEvent(String jjbh, ViewJqEsData oldData, ViewJqEsData newData) {
        try {
            ViewJqDataChangeListener.ViewJqDataUpdateEvent event =
                new ViewJqDataChangeListener.ViewJqDataUpdateEvent(jjbh, oldData, newData);
            eventPublisher.publishEvent(event);
        } catch (Exception e) {
            log.error("发布数据更新事件失败: {}", jjbh, e);
        }
    }

    /**
     * 发布数据删除事件
     */
    private void publishDeleteEvent(String jjbh, ViewJqEsData data) {
        try {
            ViewJqDataChangeListener.ViewJqDataDeleteEvent event =
                new ViewJqDataChangeListener.ViewJqDataDeleteEvent(jjbh, data);
            eventPublisher.publishEvent(event);
        } catch (Exception e) {
            log.error("发布数据删除事件失败: {}", jjbh, e);
        }
    }
}
