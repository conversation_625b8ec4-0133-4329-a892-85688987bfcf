package com.hl.test.es.service;

import com.hl.test.es.document.WjJqAnalysisDocument;
import com.hl.test.es.domain.ViewJqEsData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * ViewJqEsData 到 WjJqAnalysisDocument 数据转换服务
 * 负责数据类型转换、字段映射和数据清洗
 */
@Slf4j
@Service
public class ViewJqDataConvertService {

    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    /**
     * 单个数据转换
     * @param source ViewJqEsData 源数据
     * @return WjJqAnalysisDocument ES文档
     */
    public WjJqAnalysisDocument convertToDocument(ViewJqEsData source) {
        if (source == null) {
            return null;
        }

        WjJqAnalysisDocument document = new WjJqAnalysisDocument();
        
        try {
            // 基础信息映射
            mapBasicInfo(source, document);
            
            // 时间字段转换
            mapTimeFields(source, document);
            
            // 地理位置信息
            mapLocationInfo(source, document);
            
            // 标签信息处理
            mapLabelInfo(source, document);
            
            // 状态信息
            mapStatusInfo(source, document);
            
            // 扩展字段
            mapExtendedFields(source, document);
            
            log.debug("数据转换完成: {}", source.getJjbh());
            
        } catch (Exception e) {
            log.error("数据转换失败, jjbh: {}, error: {}", source.getJjbh(), e.getMessage(), e);
            throw new RuntimeException("数据转换失败", e);
        }
        
        return document;
    }

    /**
     * 批量数据转换
     * @param sourceList ViewJqEsData 源数据列表
     * @return WjJqAnalysisDocument ES文档列表
     */
    public List<WjJqAnalysisDocument> convertToDocuments(List<ViewJqEsData> sourceList) {
        if (sourceList == null || sourceList.isEmpty()) {
            return new ArrayList<>();
        }

        return sourceList.stream()
                .map(this::convertToDocument)
                .filter(doc -> doc != null)
                .collect(Collectors.toList());
    }

    /**
     * 映射基础信息
     */
    private void mapBasicInfo(ViewJqEsData source, WjJqAnalysisDocument document) {
        document.setJjbh(source.getJjbh());
        document.setCjbs(source.getCjbs());
        document.setBjr(source.getBjr());
        document.setBjlx(source.getBjlx());
        document.setJjdwmc(source.getJjdwmc());
        document.setJjdw(source.getJjdw());
        document.setLxdh(source.getLxdh());
        document.setBjnr(source.getBjnr());
        document.setDjdwmc(source.getDjdwmc());
        document.setSfdd(source.getSfdd());
        document.setBjxs(source.getBjxs());
        document.setJjdbh(source.getJjdbh());
        document.setJjrqsj(source.getJjrqsj());
        document.setJqdj(source.getJqdj());
    }

    /**
     * 映射时间字段
     */
    private void mapTimeFields(ViewJqEsData source, WjJqAnalysisDocument document) {
        // 报警时间
        document.setBjdhsjTime(parseStringToLocalDateTime(source.getBjdhsjTime()));
        
        // 处警时间
        document.setCjsjTime(parseStringToLocalDateTime(source.getCjsjTime()));
        
        // 事发时间
        document.setSfsjxxTime(parseStringToLocalDateTime(source.getSfsjxxTime()));
        document.setSfsjsxTime(parseStringToLocalDateTime(source.getSfsjsxTime()));
        
        // 标注时间
        document.setBzTime(source.getBzTime());
        
        // 审批时间
        document.setSpTime(source.getSpTime());
        
        // 设置修改时间为当前时间
        document.setXgsjTime(LocalDateTime.now());
        
        // 如果有Date类型的字段，进行转换
        if (source.getBjdhsj() != null) {
            document.setBjdhsjTime(source.getBjdhsj().toInstant()
                    .atZone(ZoneId.systemDefault())
                    .toLocalDateTime());
        }
        
        if (source.getCjsj() != null) {
            document.setCjsjTime(source.getCjsj().toInstant()
                    .atZone(ZoneId.systemDefault())
                    .toLocalDateTime());
        }
        
        if (source.getSfsjxx() != null) {
            document.setSfsjxxTime(source.getSfsjxx().toInstant()
                    .atZone(ZoneId.systemDefault())
                    .toLocalDateTime());
        }
        
        if (source.getSfsjsx() != null) {
            document.setSfsjsxTime(source.getSfsjsx().toInstant()
                    .atZone(ZoneId.systemDefault())
                    .toLocalDateTime());
        }
    }

    /**
     * 映射地理位置信息
     */
    private void mapLocationInfo(ViewJqEsData source, WjJqAnalysisDocument document) {
        document.setGisX(source.getGisX());
        document.setGisY(source.getGisY());
        document.setXzb(source.getXzb());
        document.setYzb(source.getYzb());
        document.setCjxxdd(source.getCjxxdd());
        document.setCjxz(source.getCjxz());
        document.setBzdzmc(source.getBzdzmc());
        document.setQylb(source.getQylb());
    }

    /**
     * 映射标签信息
     */
    private void mapLabelInfo(ViewJqEsData source, WjJqAnalysisDocument document) {
        document.setJqbz(source.getJqbz());
        document.setFjbq(source.getFjbq());
        document.setAddressm(source.getAddressm());
        document.setPersonm(source.getPersonm());
        document.setResultm(source.getResultm());
        document.setTimem(source.getTimem());
        document.setToolm(source.getToolm());
        document.setReasonm(source.getReasonm());
        
        // 处理标签列表 - 这里可以根据需要解析JSON字符串为LabelInfo列表
        // 暂时保留原始字符串，后续可以扩展
    }

    /**
     * 映射状态信息
     */
    private void mapStatusInfo(ViewJqEsData source, WjJqAnalysisDocument document) {
        document.setBzzt(source.getBzzt());
        document.setSpjg(source.getSpjg());
        document.setMark(source.getMark());
        document.setSpr(source.getSpr());
        document.setSpnr(source.getSpnr());
        document.setBzr(source.getBzr());
        document.setBzrxm(source.getBzrxm());
        document.setSprxm(source.getSprxm());
        document.setUnit(source.getUnit());
    }

    /**
     * 映射扩展字段
     */
    private void mapExtendedFields(ViewJqEsData source, WjJqAnalysisDocument document) {
        // 处警相关
        document.setCjlb(source.getCjlb());
        document.setCjdw(source.getCjdw());
        document.setCjdwmc(source.getCjdwmc());
        document.setCjjg(source.getCjjg());
        document.setCjr(source.getCjr());
        document.setCjbh(source.getCjbh());
        
        // 事件相关
        document.setSfcs(source.getSfcs());
        document.setSsxxqk(source.getSsxxqk());
        document.setBccljg(source.getBccljg());
        document.setSfxq(source.getSfxq());
        document.setCljgnr(source.getCljgnr());
        document.setDjr(source.getDjr());
        document.setJqsx(source.getJqsx());
        document.setTqqk(source.getTqqk());
        document.setSsxq(source.getSsxq());
        document.setCjjqbq(source.getCjjqbq());
        
        // 其他字段
        document.setDzType(source.getDzType());
        document.setDsdz(source.getDsdz());
        document.setJgbh(source.getJgbh());
        document.setDzid(source.getDzid());
        document.setDwmc(source.getDwmc());
        document.setDwdz(source.getDwdz());
        document.setXq(source.getXq());
        document.setZrq(source.getZrq());
        document.setFsyy(source.getFsyy());
        document.setSszrq(source.getSszrq());
        document.setEventid(source.getEventid());
    }

    /**
     * 字符串时间转LocalDateTime
     */
    private LocalDateTime parseStringToLocalDateTime(String timeStr) {
        if (!StringUtils.hasText(timeStr)) {
            return null;
        }
        
        try {
            // 尝试解析标准格式
            return LocalDateTime.parse(timeStr, DATE_TIME_FORMATTER);
        } catch (Exception e) {
            log.warn("时间格式解析失败: {}", timeStr);
            return null;
        }
    }
}
