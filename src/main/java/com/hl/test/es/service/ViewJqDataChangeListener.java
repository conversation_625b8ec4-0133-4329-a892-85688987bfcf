package com.hl.test.es.service;

import com.hl.test.es.domain.ViewJqEsData;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.concurrent.CompletableFuture;

/**
 * ViewJqEsData 数据变更监听服务
 * 监听数据变更事件，自动同步到ES
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ViewJqDataChangeListener {

    private final ViewJqDataSyncService syncService;
    private final ViewJqEsDataService viewJqEsDataService;

    /**
     * 监听数据插入事件
     * @param event 数据变更事件
     */
    @Async
    @EventListener
    public void handleDataInsert(ViewJqDataInsertEvent event) {
        log.info("监听到数据插入事件: {}", event.getJjbh());
        
        try {
            boolean success = syncService.syncSingleData(event.getJjbh());
            if (success) {
                log.info("数据插入同步成功: {}", event.getJjbh());
            } else {
                log.error("数据插入同步失败: {}", event.getJjbh());
            }
        } catch (Exception e) {
            log.error("处理数据插入事件失败: {}", event.getJjbh(), e);
        }
    }

    /**
     * 监听数据更新事件
     * @param event 数据变更事件
     */
    @Async
    @EventListener
    public void handleDataUpdate(ViewJqDataUpdateEvent event) {
        log.info("监听到数据更新事件: {}", event.getJjbh());
        
        try {
            boolean success = syncService.syncSingleData(event.getJjbh());
            if (success) {
                log.info("数据更新同步成功: {}", event.getJjbh());
            } else {
                log.error("数据更新同步失败: {}", event.getJjbh());
            }
        } catch (Exception e) {
            log.error("处理数据更新事件失败: {}", event.getJjbh(), e);
        }
    }

    /**
     * 监听数据删除事件
     * @param event 数据变更事件
     */
    @Async
    @EventListener
    public void handleDataDelete(ViewJqDataDeleteEvent event) {
        log.info("监听到数据删除事件: {}", event.getJjbh());
        
        try {
            boolean success = syncService.deleteFromEs(event.getJjbh());
            if (success) {
                log.info("数据删除同步成功: {}", event.getJjbh());
            } else {
                log.error("数据删除同步失败: {}", event.getJjbh());
            }
        } catch (Exception e) {
            log.error("处理数据删除事件失败: {}", event.getJjbh(), e);
        }
    }

    /**
     * 数据插入事件
     */
    public static class ViewJqDataInsertEvent {
        private final String jjbh;
        private final ViewJqEsData data;

        public ViewJqDataInsertEvent(String jjbh, ViewJqEsData data) {
            this.jjbh = jjbh;
            this.data = data;
        }

        public String getJjbh() {
            return jjbh;
        }

        public ViewJqEsData getData() {
            return data;
        }
    }

    /**
     * 数据更新事件
     */
    public static class ViewJqDataUpdateEvent {
        private final String jjbh;
        private final ViewJqEsData oldData;
        private final ViewJqEsData newData;

        public ViewJqDataUpdateEvent(String jjbh, ViewJqEsData oldData, ViewJqEsData newData) {
            this.jjbh = jjbh;
            this.oldData = oldData;
            this.newData = newData;
        }

        public String getJjbh() {
            return jjbh;
        }

        public ViewJqEsData getOldData() {
            return oldData;
        }

        public ViewJqEsData getNewData() {
            return newData;
        }
    }

    /**
     * 数据删除事件
     */
    public static class ViewJqDataDeleteEvent {
        private final String jjbh;
        private final ViewJqEsData data;

        public ViewJqDataDeleteEvent(String jjbh, ViewJqEsData data) {
            this.jjbh = jjbh;
            this.data = data;
        }

        public String getJjbh() {
            return jjbh;
        }

        public ViewJqEsData getData() {
            return data;
        }
    }
}

/**
 * 数据变更事件发布服务
 * 用于在数据操作时发布相应的事件
 */
@Slf4j
@Service
@RequiredArgsConstructor
class ViewJqDataEventPublisher {

    private final org.springframework.context.ApplicationEventPublisher eventPublisher;

    /**
     * 发布数据插入事件
     * @param jjbh 接警编号
     * @param data 数据对象
     */
    public void publishInsertEvent(String jjbh, ViewJqEsData data) {
        if (StringUtils.hasText(jjbh)) {
            ViewJqDataChangeListener.ViewJqDataInsertEvent event = 
                new ViewJqDataChangeListener.ViewJqDataInsertEvent(jjbh, data);
            eventPublisher.publishEvent(event);
            log.debug("发布数据插入事件: {}", jjbh);
        }
    }

    /**
     * 发布数据更新事件
     * @param jjbh 接警编号
     * @param oldData 旧数据
     * @param newData 新数据
     */
    public void publishUpdateEvent(String jjbh, ViewJqEsData oldData, ViewJqEsData newData) {
        if (StringUtils.hasText(jjbh)) {
            ViewJqDataChangeListener.ViewJqDataUpdateEvent event = 
                new ViewJqDataChangeListener.ViewJqDataUpdateEvent(jjbh, oldData, newData);
            eventPublisher.publishEvent(event);
            log.debug("发布数据更新事件: {}", jjbh);
        }
    }

    /**
     * 发布数据删除事件
     * @param jjbh 接警编号
     * @param data 数据对象
     */
    public void publishDeleteEvent(String jjbh, ViewJqEsData data) {
        if (StringUtils.hasText(jjbh)) {
            ViewJqDataChangeListener.ViewJqDataDeleteEvent event = 
                new ViewJqDataChangeListener.ViewJqDataDeleteEvent(jjbh, data);
            eventPublisher.publishEvent(event);
            log.debug("发布数据删除事件: {}", jjbh);
        }
    }
}
