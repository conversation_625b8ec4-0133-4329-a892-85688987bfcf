package com.hl.test.es.task;

import com.hl.test.es.service.PoliceCaseDataSyncService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 警情数据同步定时任务
 */
@Slf4j
@Component
@ConditionalOnProperty(name = "easy-es.enable", havingValue = "true")
public class PoliceCaseDataSyncTask {
    
    @Autowired
    private PoliceCaseDataSyncService dataSyncService;
    
    private final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    /**
     * 每小时执行一次增量同步
     * 同步最近2小时的数据（避免遗漏）
     */
    @Scheduled(cron = "0 0 * * * ?")
    public void hourlyIncrementalSync() {
        try {
            log.info("开始执行定时增量同步任务");
            
            LocalDateTime endTime = LocalDateTime.now();
            LocalDateTime startTime = endTime.minusHours(2);
            
            String startTimeStr = startTime.format(formatter);
            String endTimeStr = endTime.format(formatter);
            
            dataSyncService.incrementalSync(startTimeStr, endTimeStr);
            
            log.info("定时增量同步任务完成");
            
        } catch (Exception e) {
            log.error("定时增量同步任务失败", e);
        }
    }
    
    /**
     * 每天凌晨2点执行一次全量同步（可选，根据数据量决定）
     */
    @Scheduled(cron = "0 0 2 * * ?")
    @ConditionalOnProperty(name = "police-case.sync.full-sync-enabled", havingValue = "true")
    public void dailyFullSync() {
        try {
            log.info("开始执行定时全量同步任务");
            
            dataSyncService.fullSync();
            
            log.info("定时全量同步任务完成");
            
        } catch (Exception e) {
            log.error("定时全量同步任务失败", e);
        }
    }
}
